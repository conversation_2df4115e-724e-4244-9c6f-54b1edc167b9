import { MaterialItem } from "../../..";
import { transformCsv } from "../../transformCsv";
import { getNumber } from "../../utils";
import { HeaderArray } from "@fast-csv/parse";

export const headers = [
  "material_id",
  "material_lib",
  "canonical_smiles",
  "inchified_smiles",
  "cas_no",
  "name_en",
  "name_zh",
  "unit",
  "quantity",
  "price",
  "in_stock",
  "max_delivery_days",
  "min_delivery_days",
  "purity",
  "source",
  "source_link",
  "extension",
] as const;

export type MculeItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: MculeItem): MaterialItem => {
  return {
    material_lib: 4,

    canonical_smiles: row.canonical_smiles,
    inchified_smiles: row.inchified_smiles,
    material_id: `${row.material_id}-${getNumber(row.quantity)}-${row.unit}`,
    cas_no: row.cas_no,
    name_en: row.name_en,
    name_zh: row.name_zh,

    quantity: getNumber(row.quantity) || 0,
    unit: row.unit,
    price: getNumber(row.price) || 0,
    in_stock: !!row.in_stock,
    max_delivery_days: getNumber(row.max_delivery_days),
    min_delivery_days: getNumber(row.min_delivery_days),
    purity: row.purity,

    source: row.source,
    source_link: row.source_link,
    extension: undefined,
  };
};

export const mculeMain = async () => {
  transformCsv<MculeItem, MaterialItem>(
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule.csv",
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule_out.csv",
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule_err.csv",
    {
      headers: headers as unknown as string[],
      transform: transformWithoutSmiles,
    },
    false
  );
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
