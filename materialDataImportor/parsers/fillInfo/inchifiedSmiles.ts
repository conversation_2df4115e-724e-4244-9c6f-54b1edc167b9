import { MaterialItem } from "../../..";
import { getInchifiedSmileses } from "../format/axios";

export const addInchiFiedSmiles = async <
  T extends Partial<Pick<MaterialItem, "canonical_smiles" | "inchified_smiles">>
>(
  rows: T[]
): Promise<(T & Pick<MaterialItem, "inchified_smiles">)[]> => {
  const smilesToGet = rows
    .filter((r) => !r.inchified_smiles && r.canonical_smiles)
    .map((r) => r.canonical_smiles);
  const smiles = [...new Set(smilesToGet)];

  if (smilesToGet.length) {
    const inchieds = await getInchifiedSmileses(smiles);
    const canoToInchied = smiles.reduce<Record<string, string>>(
      (acc, cur, index) => {
        acc[cur] = inchieds[index];
        return acc;
      },
      {}
    );
    return rows.map((r) => {
      if (r.inchified_smiles)
        return r as T & Pick<MaterialItem, "inchified_smiles">;
      return { ...r, inchified_smiles: canoToInchied[r.canonical_smiles] };
    });
  }
  return rows as (T & Pick<MaterialItem, "inchified_smiles">)[];
};
