import { singleQuery } from "./base";
import { promisify } from "util";
import { exec } from "child_process";
import { Provider } from "../run";
import { providerIdMap } from "../parsers/provider/idMap";
import { orderedHeaders } from "../utils";

const execAsync = promisify(exec);

export const dbMain = async (
  type: Provider,
  version: string,
  outDir: string,
  updator: number = 1,
  forceUpdate: boolean = false
) => {
  const libId = providerIdMap[type];
  const oldVersion = await singleQuery(
    `select "version"  from material_libs  where id = ${libId}`
  );
  if (!oldVersion?.[0]?.version) {
    throw `No lib for type ${type} with id ${libId}`;
  }
  const higherVersion = oldVersion[0].version < version;
  if (!higherVersion && !forceUpdate) {
    throw `The new version (${version}) should be higher than current ${oldVersion[0].version}`;
  } else if (!higherVersion && forceUpdate) {
    console.warn(
      `Force update lib ${type} from ${oldVersion[0].version} to version ${version}`
    );
  }

  console.log(`Start deleting old material items...`);
  const { rowCount } = await singleQuery(
    `delete from material_items where material_lib_id = '${libId}'`
  );
  console.log(`End deleting old material items, ${rowCount} rows deleted\n`);

  console.log(`Start copy data into db...`);
  const copyResult = await copyCsv(`${outDir}/${type}-${version}.csv`);
  if (copyResult === false) {
    console.error("Copy data error, please check logs");
    return;
  }
  console.log(`End copy, ${copyResult}\n`);

  console.log(`Start link item and lib...`);
  const linkRes = await singleQuery(addLinkSql(libId));
  console.log(`End link, add ${linkRes.rowCount} rows\n`);

  console.log(`Start calc the lowest unit price column...`);
  const lowestRes = await singleQuery(calcLowest(libId));
  console.log(`End calc, update ${lowestRes.rowCount} rows\n`);

  console.log(`Start update lib info...`);
  const updateRes = await singleQuery(updateLib(libId, version, updator));
  console.log(`End update lib info\n`);
};

const copyCsv = async (filePath: string) => {
  try {
    const { stdout, stderr } = await execAsync(
      [
        `psql`,
        `-U ${process.env.DB_USER}`,
        `-d ${process.env.DB_DATABASE}`,
        `-h ${process.env.DB_HOST}`,
        `-p ${process.env.DB_PORT}`,
        `-c ${copyCommand(filePath)}`,
      ].join(" ")
    );
    if (stderr) {
      throw stderr;
    }
    return stdout;
  } catch (error) {
    console.error("Execution error:", error);
    return false;
  }
};

const copyCommand = (filePath: string) => {
  return `"\\copy material_items (
    ${orderedHeaders
      .map((c) => `"${c === "material_lib" ? "material_lib_id" : c}"`)
      .join(",\n")}
    )
    from
    '${filePath}' with (FORMAT csv, header true, delimiter ',');"`;
};

const addLinkSql = (libId: number | string) => `
  WITH RankedItems AS (
    SELECT 
        mi.id AS material_item_id, 
        ml.id::INT AS material_lib_id,
        ROW_NUMBER() OVER (PARTITION BY ml.id ORDER BY mi.id ASC) AS material_item_order
    FROM 
        material_items mi
    JOIN 
        material_libs ml 
    ON 
        mi.material_lib_id = ml.id::VARCHAR
    WHERE
        ml.id = ${libId}
  )
  INSERT INTO material_items_material_lib_links (material_item_id, material_lib_id, material_item_order)
  SELECT 
    material_item_id, 
    material_lib_id, 
    material_item_order
  FROM 
    RankedItems
  ON CONFLICT
  DO nothing;
`;

const calcLowest = (libId: number | string) => `
WITH RankedPrices AS (
  SELECT
      id,
      material_lib_id,
      inchified_smiles,
      unit_price,
      rank() OVER (PARTITION BY material_lib_id, inchified_smiles ORDER BY unit_price ASC) AS rank
  FROM material_items
  WHERE
      unit_price IS NOT NULL
      and
      material_lib_id = '${libId}'
)
UPDATE material_items
SET lowest_unit_price = (RankedPrices.rank = 1)
FROM RankedPrices
WHERE material_items.id = RankedPrices.id;
`;

const updateLib = (
  libId: number | string,
  version: string,
  updatorId: string | number
) => `
UPDATE material_libs
SET
	"version" = '${version}',
	updated_at = NOW(),
  last_update_time = NOW(),
  updated_by_id = ${updatorId}
WHERE id = ${libId}
`;
