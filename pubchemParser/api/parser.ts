import { ParsedInfo } from "..";
import { Information, PubChemViewResponse, Section, Value } from "./type";

export const parseEntryTerm = (
  info: PubChemViewResponse
): ParsedInfo["entryTerm"] => {
  const s = getSectionFromHeads(info, [
    "Names and Identifiers",
    "Synonyms",
    "MeSH Entry Terms",
  ]);
  const value = s?.Information?.[0]?.Value;
  if (!value) return [];
  return parseValueToStrings(value);
};

export const parseSynonyms = (
  info: PubChemViewResponse
): ParsedInfo["synonyms"] => {
  const s = getSectionFromHeads(info, [
    "Names and Identifiers",
    "Synonyms",
    "Depositor-Supplied Synonyms",
  ]);
  const value = s?.Information?.[0]?.Value;
  if (!value) return [];
  return parseValueToStrings(value);
};

export const parseComputedProperties = (
  info: PubChemViewResponse
): ParsedInfo["computedProperties"] => {
  const s = getSectionFromHeads(info, [
    "Chemical and Physical Properties",
    "Computed Properties",
  ]);
  if (!s) return {};
  return s.Section.reduce<Record<string, string>>((acc, cur) => {
    const key = cur.TOCHeading;
    const value = parseInfoToString(cur.Information?.[0]);
    acc[key] = value;
    return acc;
  }, {});
};

export const parseExperimentalProperties = (
  info: PubChemViewResponse
): ParsedInfo["experimentalProperties"] => {
  const s = getSectionFromHeads(info, [
    "Chemical and Physical Properties",
    "Experimental Properties",
  ]);
  if (!s) return {};
  return s?.Section.reduce<
    Record<string, { origin: string; parsed: string }[]>
  >((acc, cur) => {
    const key = cur.TOCHeading;
    const origins = cur.Information.map(parseInfoToString);
    acc[key] = origins.map((origin) => ({
      origin,
      parsed: parseProperty(origin),
    }));
    return acc;
  }, {});
};

const getSectionFromHeads = (
  { Record }: PubChemViewResponse,
  paths: string[]
): Section | undefined => {
  let cur: Section = Record as unknown as Section;
  for (const path of paths) {
    cur = cur?.Section?.find((s) => s.TOCHeading === path);
    if (!cur) return;
  }
  return cur;
};

const parseValueToStrings = (value: Value): string[] => {
  return value.StringWithMarkup.map((s) => {
    return s.String;
  });
};

const parseInfoToString = ({ Name, Value }: Information): string => {
  let s = "";
  if (Name) s += Name;
  if (Value) {
    const { Number, StringWithMarkup, Unit } = Value;
    if (Name) s += ": ";
    if (Number?.length) s += Number.join(", ");
    if (StringWithMarkup?.length)
      s += StringWithMarkup.map((s) => s.String).join(", ");
    if (Unit) s += Unit;
  }
  return s;
};

const parseProperty = (origin: string): string => {
  return origin;
};
