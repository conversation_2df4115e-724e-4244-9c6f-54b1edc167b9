# Brain Server Client

一个用于调用 brain-server API 处理 SMILES 字符串的 Python 客户端工具。

## 文件说明

- `brain_server_client.py` - 主要的客户端模块
- `brain_client_example.py` - 使用示例代码
- `requirements_brain_client.txt` - 依赖包列表
- `README_brain_client.md` - 详细文档
- `brain-server.openapi.json` - API 规范文件

## 快速开始

### 方法一：使用 Poetry（推荐）

1. 确保已安装 Poetry：
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. 运行安装脚本：
```bash
./setup.sh
```

3. 激活虚拟环境：
```bash
poetry shell
```

### 方法二：使用 pip

```bash
pip install -r requirements_brain_client.txt
```

2. 基本使用：
```python
from brain_server_client import BrainServerClient

# 创建客户端
client = BrainServerClient(base_url="http://localhost:8080")

# 处理 SMILES
smiles_list = ["CCO", "CC(=O)O", "c1ccccc1"]
results = client.process_smiles_sync(smiles_list)

# 查看结果
for i, result in enumerate(results):
    print(f"Input: {smiles_list[i]}")
    if result['canonical_smiles'] is None:
        print("  Invalid SMILES")
    else:
        print(f"  Canonical: {result['canonical_smiles']}")
        print(f"  Inchified: {result['inchified_smiles']}")
```

## 功能特性

- ✅ 支持批量处理 SMILES 字符串
- ✅ 自动去重和结果映射
- ✅ 异步并行处理
- ✅ 无效 SMILES 检测（返回 None）
- ✅ 可配置批次大小和并发限制
- ✅ 完整的类型注解

更多详细信息请查看 `README_brain_client.md`。