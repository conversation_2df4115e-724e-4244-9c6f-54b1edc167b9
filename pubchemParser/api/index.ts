import axios from "axios";
import axiosRetry, { IAxiosRetryConfig } from "axios-retry";
import pLimit from "p-limit";
import { ParsedInfo } from "..";
import {
  parseComputedProperties,
  parseEntryTerm,
  parseExperimentalProperties,
  parseSynonyms,
} from "./parser";
import { PubChemViewResponse } from "./type";

const defaultRetryConfig: IAxiosRetryConfig = {
  retries: 3,
  onRetry: (c, e) => {
    console.log(`retry ${c} times...`);
    console.error(e);
  },
  retryDelay: axiosRetry.exponentialDelay,
};

const pubchemBase = `https://pubchem.ncbi.nlm.nih.gov/rest/pug_view`;
const server = axios.create({ baseURL: pubchemBase });
axiosRetry(server, { ...defaultRetryConfig });

const limit = pLimit(5);
const isCas = /^[1-9]{1}[0-9]{1,5}-\d{2}-\d$/;

const fetchPubchemInfo = async (id: number): Promise<PubChemViewResponse> => {
  const { data } = await limit(() => server.get(`/data/compound/${id}/JSON`));
  return data;
};

const parse = (data: PubChemViewResponse): ParsedInfo => {
  const entryTerm = parseEntryTerm(data);
  const synonyms = parseSynonyms(data);
  const computedProperties = parseComputedProperties(data);
  const experimentalProperties = parseExperimentalProperties(data);

  const casLikes = [...entryTerm, ...synonyms].filter((s) => isCas.test(s));

  return {
    entryTerm,
    synonyms,
    casLikes,
    computedProperties,
    experimentalProperties,
  };
};

export const getPubchemInfoFromApi = async (
  id: number
): Promise<ParsedInfo> => {
  console.log(`Fetching pubchem info for ${id}...`);
  const data = await fetchPubchemInfo(id);
  console.log(`Finish fetching pubchem info for ${id}`);

  return parse(data);
};
