import { CheerioAPI } from "cheerio";

export const parseEntryTerm = ($: CheerioAPI): string[] => {
  const lis = $("#MeSH-Entry-Terms ul:first-child li");
  const terms: string[] = [];
  for (const li of lis) {
    const text = $(li).text().trim();
    if (text) terms.push(text);
  }
  return terms;
};

export const parseSynonyms = ($: CheerioAPI): string[] => {
  const as = $("#Depositor-Supplied-Synonyms ul:first-child li a");
  const synonyms: string[] = [];
  for (const a of as) {
    const text = $(a).text().trim();
    if (text) synonyms.push(text);
  }
  return synonyms;
};

export const parseComputedProperties = (
  $: CheerioAPI
): Record<string, string> => {
  const rows = $("#Computed-Properties .sm\\:table-row-group .sm\\:table-row");
  const properties: Record<string, string> = {};
  for (const r of rows) {
    const prop = $(r).find(`div:contains("Property Name")`).next().text();
    const value = $(r).find(`div:contains("Property Value")`).next().text();
    properties[prop] = value;
  }
  return properties;
};

export const parseExperimentalProperties = (
  $: CheerioAPI
): Record<string, { origin: string; parsed: string }[]> => {
  const properties = {};
  const sections = $("#Experimental-Properties section[id]");
  for (const s of sections) {
    const e = $(s);
    const name = e.find("h4").text().split(" ").slice(1).join(" ");
    const tableEls = e.find("div.sm\\:table-row-group");
    if (tableEls.length > 0) {
      const rows = $(tableEls[0]).find("div.sm\\:table-row");
      let pv = "";
      for (const row of rows) {
        const cells = $(row).find("div.sm\\:table-cell");
        const key = $(cells[0]).text().trim();
        const value = $(cells.slice(1)).text().trim();
        pv += `${key}: ${value}\n`;
      }
      properties[name] = pv;
      continue;
    }
    const values = Array.from(e.find("div.break-words")).map((e) => {
      const el = $(e);
      let text = el.text().trim();
      if (el.prev().is("div.font-medium")) {
        text = `${el.prev().text().trim()}\n${text}`;
      }
      return text;
    });
    properties[name] = values.map((v) => ({ origin: v, parsed: v }));
  }
  return properties;
};
