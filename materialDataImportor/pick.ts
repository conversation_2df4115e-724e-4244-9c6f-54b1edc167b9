import { getReadStream, getWriteStream } from './utils';

export const pick = async (
  inPath: string,
  outPath: string,
  picker: (item: any) => boolean,
): Promise<void> => {
  return new Promise(async (resolve, reject) => {
    const inStream = getReadStream(inPath);
    const [outStream, pipe] = getWriteStream(outPath, resolve, reject);

    inStream
      .on('data', async (row) => {
        if (picker(row)) outStream.write(row);
      })
      .on('error', (error) => {
        reject(error);
      })
      .on('end', async () => {});

    pipe();
  });
};
