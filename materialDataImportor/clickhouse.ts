import PQueue from "p-queue";
import <PERSON><PERSON>House from "@apla/clickhouse";
import { MaterialItem } from "..";
import { assign, chunk, groupBy } from "lodash";
import { PubchemSqlData } from "../pubchemParser/jsonToDb";

const queryQueue = new PQueue({ concurrency: 5 });

const clickhouse = new ClickHouse({
  host: "ec2-161-189-187-79.cn-northwest-1.compute.amazonaws.com.cn",
  port: 8123,
  debug: false,
  isUseGzip: false,
  format: "JSON",
});

export const synonymSql = (cases: string[]): string => `
  select *
  from pubchem_synonyms
  where synonym in [${cases
    .map((c) => `'${c.replace(/'/g, `\\'`)}'`)
    .join(", ")}]`;

const inchifiedSmilesSql = (smiles: string[]): string => `
  select *
  from pubchem_synonyms
  where inchified_smiles in [${smiles
    .map((c) => `'${c.replace(/'/g, `\\'`)}'`)
    .join(", ")}]`;

const casSql = (cases: string[]): string => `
  SELECT x.*
  FROM pubchem1 x
  WHERE hasAny(splitByString('|', casid), [${cases
    .map((c) => `'${c}'`)
    .join(", ")}])`;

const inchifiedSmilesWithCodeSql = (smiles: string[]): string => `
  SELECT x.*
  FROM pubchem_hazards x
  where inchi_smi in [${smiles
    .map((c) => `'${c.replace(/'/g, `\\'`)}'`)
    .join(", ")}]`;

export type Basic = Pick<
  MaterialItem,
  "canonical_smiles" | "inchified_smiles"
> & {
  cid: string;
  query: string;
  synonym: string;
};
type MaterialWithCodeBasic = Pick<MaterialItem, "inchified_smiles"> & {
  query: string;
  hazards: string;
  cid: string;
};

type MaterialWithCodeResult = Pick<MaterialItem, "inchified_smiles"> & {
  query: string;
  codes: string[];
  pubchem_safety_link: string;
};
type SmilesResult = Pick<
  MaterialItem,
  "canonical_smiles" | "inchified_smiles"
> & {
  query: string;
  synonyms: string[];
};

const maxBatchSize = 1000;

export const getInfoFromCasOrNameBatch = async (
  querys: Set<string>
): Promise<Record<string, Basic[]>> => {
  const results = await Promise.all(
    chunk([...querys], maxBatchSize).map(getInfoFromCasOrNames)
  );
  return assign(results[0], ...results.slice(1));
};

const getInfoFromCasOrNames = async (
  querys: string[]
): Promise<Record<string, Basic[]>> => {
  if (querys.length > maxBatchSize) {
    console.error(`over max query size: ${querys.length}`);
    return {};
  }
  return queryQueue.add(async () => {
    console.debug(`Start quering ${querys.length} synonym`);

    const t1 = performance.now();
    const result = await clickhouse.querying(synonymSql(Array.from(querys)));
    const t2 = performance.now();
    console.debug(`query ${querys.length} synonym cost: ${(t2 - t1) / 1000}s`);

    const resultObjects = (result.data as any[]).map<Basic>((r) => ({
      cid: r.cid,
      query: r.synonym,
      synonym: r.synonym,
      canonical_smiles: r.canonical_smiles,
      inchified_smiles: r.inchified_smiles,
      codes: r.codes,
    }));
    return groupBy(resultObjects, (o) => o.query);
  });
};

export const getInfoFromInchifiedSmilesBatch = async (
  querys: Set<string>
): Promise<Record<string, SmilesResult>> => {
  const results = await Promise.all(
    chunk([...querys], maxBatchSize).map(getInfoFromInchifiedSmileses)
  );
  return assign(results[0], ...results.slice(1));
};

const getInfoFromInchifiedSmileses = async (
  querys: string[]
): Promise<Record<string, SmilesResult>> => {
  if (querys.length > maxBatchSize) {
    console.error(`over max query size: ${querys.length}`);
    return {};
  }
  return queryQueue.add(async () => {
    console.log(`Start quering ${querys.length} smiles`);

    const t1 = performance.now();
    const result = await clickhouse.querying(
      inchifiedSmilesSql(Array.from(querys))
    );
    const t2 = performance.now();
    console.log(`query ${querys.length} smiles cost: ${(t2 - t1) / 1000}s`);

    const resultObjects = (result.data as any[]).map<Basic>((r) => ({
      cid: r.cid,
      query: r.inchified_smiles,
      synonym: r.synonym,
      canonical_smiles: r.canonical_smiles,
      inchified_smiles: r.inchified_smiles,
    }));
    const grouped = groupBy(resultObjects, (o) => o.query);
    return Object.entries(grouped).reduce<Record<string, SmilesResult>>(
      (acc, [key, items]) => {
        acc[key] = items.reduce<SmilesResult>(
          (acc, cur) => ({
            query: key,
            inchified_smiles: acc.inchified_smiles || cur.inchified_smiles,
            canonical_smiles: acc.canonical_smiles || cur.canonical_smiles,
            synonyms: [...(acc.synonyms || []), cur.synonym],
          }),
          {} as SmilesResult
        );
        return acc;
      },
      {}
    );
  });
};

export const getCodeFromInchifiedSmilesBatch = async (
  querys: Set<string>
): Promise<Record<string, MaterialWithCodeResult>> => {
  const results = await Promise.all(
    chunk([...querys], maxBatchSize).map(getCodeFromInchifiedSmileses)
  );
  return assign(results[0], ...results.slice(1));
};

const getCodeFromInchifiedSmileses = async (
  querys: string[]
): Promise<Record<string, MaterialWithCodeResult>> => {
  if (querys.length > maxBatchSize) {
    console.error(`over max query size: ${querys.length}`);
    return {};
  }
  return queryQueue.add(async () => {
    console.log(`Start quering ${querys.length} smiles for codes`);

    const t1 = performance.now();
    const result = await clickhouse.querying(
      inchifiedSmilesWithCodeSql(Array.from(querys))
    );
    const t2 = performance.now();
    console.log(
      `query ${querys.length} smiles for codes cost: ${(t2 - t1) / 1000}s`
    );

    const resultObjects = (result.data as any[]).map<MaterialWithCodeBasic>(
      (r) => ({
        cid: r.cid,
        query: r.inchi_smi,
        inchified_smiles: r.inchi_smi,
        hazards: r.hazards,
      })
    );
    const grouped = groupBy(resultObjects, (o) => o.query);
    return Object.entries(grouped).reduce<
      Record<string, MaterialWithCodeResult>
    >((acc, [key, items]) => {
      acc[key] = items.reduce<MaterialWithCodeResult>(
        (acc, cur) => ({
          query: key,
          inchified_smiles: acc.inchified_smiles || cur.inchified_smiles,
          codes: [...(acc.codes || []), ...(cur.hazards.split("|") || [])],
          pubchem_safety_link: getPubchemSafetyLink(cur.cid),
        }),
        {} as MaterialWithCodeResult
      );
      return acc;
    }, {});
  });
};

export const getSmilesFromCasBatch = async (
  cases: Set<string>
): Promise<Record<string, string>> => {
  const results = await Promise.all(
    chunk([...cases], maxBatchSize).map(getSmilesFromCases)
  );
  return assign(results[0], ...results.slice(1));
};

const getSmilesFromCases = async (
  cases: string[]
): Promise<Record<string, string>> => {
  if (cases.length > maxBatchSize) {
    console.error(`over max query size: ${cases.length}`);
    return {};
  }
  return queryQueue.add(async () => {
    console.debug(`Start quering ${cases.length} cas`);

    const t1 = performance.now();
    const resultObjects = await clickhouse.querying(casSql(Array.from(cases)));
    const t2 = performance.now();
    console.debug(`query ${cases.length} cas cost: ${(t2 - t1) / 1000}s`);

    return resultObjects.data.reduce<Record<string, string>>((acc, cur) => {
      cur.casid.split("|").forEach((cas) => {
        acc[cas] = cur.smi;
      });
      return acc;
    }, {});
  });
};

const toSqlString = (obj: any): string => {
  return JSON.stringify(obj).replace(/'/g, "\\'");
};

const toSqlArray = (strs: string[]): string => {
  return "[" + strs.map((s) => `'${s}`).join(", ") + "]";
};

const updateSql = ({ id, data }: PubchemSqlData): string => {
  return `
    ALTER TABLE default.pubchem1
    UPDATE
      entry_term = ${toSqlArray(data.entry_term)},
      synonyms = ${toSqlArray(data.synonyms)},
      cas_like = ${toSqlArray(data.cas_like)},
      computed_properties = ${toSqlString(data.computed_properties)},
      experimental_properties = ${toSqlString(data.experimental_properties)},
    WHERE cid = ${id}`;
};

export const update = async (data: PubchemSqlData) => {
  try {
    const result = await clickhouse.query(updateSql(data));
    console.log("Update successful:", result);
  } catch (error) {
    console.error("Error during update:", error);
  }
};

const getPubchemSafetyLink = (cid: string): string => {
  return `https://pubchem.ncbi.nlm.nih.gov/compound/${cid}#section=Safety-and-Hazards`;
};
