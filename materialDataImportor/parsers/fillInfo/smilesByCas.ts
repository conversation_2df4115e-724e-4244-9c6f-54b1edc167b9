import { MaterialItem } from "../../..";
import { getSmilesFromCasBatch } from "../../clickhouse";

export const addSmilesByCas = async <
  T extends Partial<
    Pick<MaterialItem, "inchified_smiles" | "canonical_smiles" | "cas_no">
  >
>(
  rows: T[]
): Promise<(T & Pick<MaterialItem, "cas_no">)[]> => {
  const querys = new Set<string>(
    rows.filter((r) => !r.canonical_smiles && r.cas_no).map((r) => r.cas_no)
  );

  if (querys.size) {
    const casToSmiles = await getSmilesFromCasBatch(querys);
    return rows.map((r) => ({
      ...r,
      canonical_smiles: r.canonical_smiles || casToSmiles[r.cas_no] || "",
    }));
  }
  return rows as (T &
    Pick<MaterialItem, "inchified_smiles" | "cas_no" | "name_en">)[];
};
