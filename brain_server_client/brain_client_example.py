#!/usr/bin/env python3
"""
Example usage of Brain Server Client

This example demonstrates how to use the brain_server_client module
to process SMILES strings and get canonical and inchified versions.
"""

import os
import asyncio
from brain_server_client import BrainServerClient, process_smiles


def example_basic_usage():
    """Basic usage example with the convenience function"""
    print("=== Basic Usage Example ===")

    # Configuration
    base_url = os.getenv("BRAIN_SERVER_URL", "https://brain-uat.labwise.cn/api")
    bearer_token = os.getenv("BRAIN_SERVER_TOKEN", "")

    # Test SMILES (including duplicates to show deduplication)
    test_smiles = [
        "CCO",  # ethanol
        "CC(=O)O",  # acetic acid
        "CCO",  # duplicate ethanol
        "c1ccccc1",  # benzene
        "CC(C)O",  # isopropanol
        "CCO",  # another duplicate ethanol
        "C1CC2C3456C7C(CCCC(C3)(CCCCC4)C5)C345C89C%10%11%12%13(CCCCC3CC%10)(C38C8C94CC6C4692C2%10CC(CCC%14(C%154(C3)CCCCC3(CC%15(C%11)(C%14C3)(C%12)C678C2)CC%13)CC%10)(CC9)C1)C5",
    ]

    print(f"Input SMILES ({len(test_smiles)} total, with duplicates):")
    for i, smiles in enumerate(test_smiles, 1):
        print(f"  {i}. {smiles}")
    print()

    try:
        # Process SMILES
        results = process_smiles(test_smiles, base_url, bearer_token)

        print("Results (maintaining input order):")
        for i, (input_smiles, result) in enumerate(zip(test_smiles, results), 1):
            print(f"  {i}. Input: {input_smiles}")
            canonical = result["canonical_smiles"]
            inchified = result["inchified_smiles"]
            print(
                f"     Canonical: {canonical if canonical is not None else 'Invalid SMILES'}"
            )
            print(
                f"     Inchified: {inchified if inchified is not None else 'Invalid SMILES'}"
            )
            print()

    except Exception as e:
        print(f"Error: {e}")
        print("Note: This example requires valid API credentials and server URL.")
        print("Set BRAIN_SERVER_URL and BRAIN_SERVER_TOKEN environment variables.")


def example_single_smiles():
    """Example with a single SMILES string"""
    print("=== Single SMILES Example ===")

    base_url = os.getenv("BRAIN_SERVER_URL", "https://brain-uat.labwise.cn/api")
    bearer_token = os.getenv("BRAIN_SERVER_TOKEN", "")

    single_smiles = "CC(=O)Oc1ccccc1C(=O)O"  # aspirin

    print(f"Input: {single_smiles}")

    try:
        results = process_smiles(single_smiles, base_url, bearer_token)

        # Even for single input, result is a list with one element
        result = results[0]
        canonical = result["canonical_smiles"]
        inchified = result["inchified_smiles"]
        print(f"Canonical: {canonical if canonical is not None else 'Invalid SMILES'}")
        print(f"Inchified: {inchified if inchified is not None else 'Invalid SMILES'}")

    except Exception as e:
        print(f"Error: {e}")


async def example_async_usage():
    """Example using the async client directly"""
    print("=== Async Usage Example ===")

    base_url = os.getenv("BRAIN_SERVER_URL", "https://brain-uat.labwise.cn/api")
    bearer_token = os.getenv("BRAIN_SERVER_TOKEN", "")

    # Create client
    client = BrainServerClient(base_url, bearer_token, batch_size=5000)

    # Large list to demonstrate batching
    large_smiles_list = [
        "CCO",
        "CC(=O)O",
        "c1ccccc1",
        "CC(C)O",
        "CCN",
        "CCC",
        "CCCO",
        "CCCCO",
        "CC(C)C",
        "CC(C)(C)O",
    ] * 1000  # 10,000 SMILES to test batching

    print(f"Processing {len(large_smiles_list)} SMILES (with batching)...")

    try:
        results = await client.process_smiles(large_smiles_list)

        print(f"Successfully processed {len(results)} SMILES")

        # Show first few results
        print("\nFirst 5 results:")
        for i in range(min(5, len(results))):
            print(f"  {i + 1}. Input: {large_smiles_list[i]}")
            canonical = results[i].canonical_smiles
            inchified = results[i].inchified_smiles
            print(
                f"     Canonical: {canonical if canonical is not None else 'Invalid SMILES'}"
            )
            print(
                f"     Inchified: {inchified if inchified is not None else 'Invalid SMILES'}"
            )

    except Exception as e:
        print(f"Error: {e}")


def example_large_dataset():
    """Example with a large dataset to demonstrate batching"""
    print("=== Large Dataset Example ===")

    base_url = os.getenv("BRAIN_SERVER_URL", "https://brain-uat.labwise.cn/api")
    bearer_token = os.getenv("BRAIN_SERVER_TOKEN", "")

    # Simulate a large dataset
    base_smiles = [
        "CCO",
        "CC(=O)O",
        "c1ccccc1",
        "CC(C)O",
        "CCN",
        "C1CC2C3456C7C(CCCC(C3)(CCCCC4)C5)C345C89C%10%11%12%13(CCCCC3CC%10)(C38C8C94CC6C4692C2%10CC(CCC%14(C%154(C3)CCCCC3(CC%15(C%11)(C%14C3)(C%12)C678C2)CC%13)CC%10)(CC9)C1)C5",
    ]
    large_dataset = base_smiles * 2500  # 12,500 SMILES

    print(f"Processing large dataset with {len(large_dataset)} SMILES...")
    print(f"This will be processed in batches of 10,000")

    try:
        # Use smaller batch size for demonstration
        client = BrainServerClient(base_url, bearer_token, batch_size=5000)
        results = client.process_smiles_sync(large_dataset)

        print(f"Successfully processed {len(results)} SMILES")

        # Count unique results
        unique_canonical = set(r.canonical_smiles for r in results)
        unique_inchified = set(r.inchified_smiles for r in results)

        print(f"Unique canonical SMILES: {len(unique_canonical)}")
        print(f"Unique inchified SMILES: {len(unique_inchified)}")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    print("Brain Server Client Examples\n")

    # Check if environment variables are set
    if not os.getenv("BRAIN_SERVER_URL") or not os.getenv("BRAIN_SERVER_TOKEN"):
        print(
            "Warning: BRAIN_SERVER_URL and BRAIN_SERVER_TOKEN environment variables not set."
        )
        print(
            "Examples will show the expected behavior but may fail without valid credentials.\n"
        )

    # Run examples
    example_basic_usage()
    print("\n" + "=" * 50 + "\n")

    example_single_smiles()
    print("\n" + "=" * 50 + "\n")

    # Run async example
    print("Running async example...")
    asyncio.run(example_async_usage())
    print("\n" + "=" * 50 + "\n")

    example_large_dataset()
