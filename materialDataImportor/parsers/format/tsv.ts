import {
  testSize,
  getReadStream,
  getWriteStreamWithPromise,
} from "../../utils";
import { FormatterRow, ParserHeaderArray } from "fast-csv";
import { config as emoleculeConfig } from "../provider/emolecule";
import { MaterialItem } from "../../..";
import { handleStream } from "../stream";

export type ProdiverType = "emolecule";

interface ParserConfig<T extends FormatterRow = FormatterRow> {
  headers: ParserHeaderArray;
  transform: (row: T) => Promise<MaterialItem | false>;
}

const configs: Record<ProdiverType, ParserConfig> = {
  emolecule: emoleculeConfig,
};

export const parseToCsv = async (
  type: ProdiverType,
  inPath: string,
  outDir: string,
  version: string,
  testMode: boolean = true
) => {
  const { headers, transform } = configs[type];

  const [outStream, pipe, outP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}.csv`
  );
  const [errStream, errPipe, errP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}-err.csv`
  );

  const readSream = getReadStream(inPath, {
    delimiter: "\t",
    headers: headers,
    renameHeaders: true,
    maxRows: testMode ? testSize : undefined,
  });

  pipe();
  errPipe();
  await handleStream(transform, readSream, outStream, errStream);
  outStream.end();
  errStream.end();

  return Promise.all([outP, errP]);
};
