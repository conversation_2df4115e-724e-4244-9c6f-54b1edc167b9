import { MaterialItem } from "../../..";
import { getCodeFromInchifiedSmilesBatch } from "../../clickhouse";

type PickInfo = Pick<MaterialItem, "codes" | "pubchem_safety_link">;
export const addCodes = async <
  T extends Partial<Pick<MaterialItem, "inchified_smiles" | "codes">>
>(
  rows: T[]
): Promise<(T & PickInfo)[]> => {
  const smilesToGet = rows
    .filter((r) => r.inchified_smiles && !r.codes?.length)
    .map((r) => r.inchified_smiles);

  if (smilesToGet.length) {
    const infos = await getCodeFromInchifiedSmilesBatch(new Set(smilesToGet));

    return rows.map((r) => {
      if (r.codes?.length) return { ...r, codes: [], pubchem_safety_link: "" };

      const result = infos[r.inchified_smiles];
      if (!result) {
        return { ...r, codes: [], pubchem_safety_link: "" };
      }
      return {
        ...r,
        codes: result.codes,
        pubchem_safety_link: result.pubchem_safety_link,
      };
    });
  }
  return rows as (T & PickInfo)[];
};
