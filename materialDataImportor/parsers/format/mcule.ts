import { testSize, getWriteStreamWithPromise } from "../../utils";
import { FormatterRow, parse, ParserHeaderArray } from "fast-csv";
import { config as mculeConfig } from "../provider/mcule";
import { MaterialItem } from "../../..";
import { getMculeDataAsStream } from "./axios";
import { handleStream } from "../stream";

export type ProdiverType = "mcule";

interface ParserConfig<T extends FormatterRow = FormatterRow> {
  headers: ParserHeaderArray;
  transform: (row: T) => Promise<Partial<MaterialItem> | false>;
}

const configs: Record<ProdiverType, ParserConfig> = {
  mcule: mculeConfig,
};

export const parseToCsv = async (
  type: "mcule",
  outDir: string,
  oldVersion?: string,
  testMode: boolean = true
) => {
  const { headers, transform } = configs[type];

  const [inStream, newVersion] = (await getMculeDataAsStream(oldVersion)) || [];
  if (!newVersion) return;

  const [outStream, pipe, outP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${newVersion}.csv`
  );
  const [errStream, errPipe, errP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${newVersion}-err.csv`
  );

  const parseStream = inStream.pipe(
    parse({
      delimiter: "\t",
      headers,
      renameHeaders: true,
      maxRows: testMode ? testSize : undefined,
    })
  );

  pipe();
  errPipe();
  await handleStream(transform, parseStream, outStream, errStream);
  outStream.end();
  errStream.end();

  return Promise.all([newVersion, outP, errP]);
};
