import * as fs from "fs";
import * as fastcsv from "fast-csv";
import { validate } from "./utils";
import { MaterialItem } from "..";

const processBatch = async <
  InputRow extends fastcsv.FormatterRow,
  OutputRow extends MaterialItem
>(
  batch: OutputRow[],
  errFormatStream: fastcsv.CsvFormatterStream<InputRow, OutputRow>,
  formatStream: fastcsv.CsvFormatterStream<InputRow, OutputRow>
) => {
  try {
    const smilesToGet = batch.filter((b) => !b.canonical_smiles);
    let casToSmiles: Map<string, string> = new Map();
    if (smilesToGet.length) {
      casToSmiles = new Map(); // TODO
    }
    batch
      .map((b) =>
        validate({
          ...b,
          canonical_smiles:
            b.canonical_smiles || casToSmiles.get(b.cas_no || "") || "",
        })
      )
      .forEach((b) => {
        if (b.error) {
          errFormatStream.write(b);
        } else {
          formatStream.write(b);
        }
      });
  } catch (error) {
    console.log(error);
    // errFormatStream.write({ error: JSON.stringify(error) });
  }
};

const batchSize = 1000;

export const transformCsv = async <
  InputRow extends fastcsv.FormatterRow,
  OutputRow extends MaterialItem
>(
  inputFilePath: string,
  outputFilePath: string,
  errFilePath: string,
  config: {
    headers: fastcsv.ParserHeaderArray;
    transform: (row: InputRow) => OutputRow;
    delimiter?: string;
  },
  testMode: boolean = true
): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    let batch: OutputRow[] = [];
    const readStream = fs.createReadStream(inputFilePath);
    const parseStream = fastcsv.parse<InputRow, OutputRow>({
      headers: config.headers,
      renameHeaders: true,
      delimiter: config.delimiter,
      maxRows: testMode ? batchSize : undefined,
    });

    const writeStream = fs.createWriteStream(outputFilePath);
    const errWriteStream = fs.createWriteStream(errFilePath);

    const formatStream = fastcsv.format<InputRow, OutputRow>({ headers: true });
    const errFormatStream = fastcsv.format<InputRow, OutputRow>({
      headers: true,
    });

    parseStream
      .on("data", async (row) => {
        batch.push(config.transform(row));
        if (batch.length >= batchSize) {
          const copied = batch.concat();
          batch = [];
          await processBatch<InputRow, OutputRow>(
            copied,
            errFormatStream,
            formatStream
          );
        }
      })
      .on("error", (error) => {
        reject(error);
      })
      .on("end", async () => {
        if (batch.length >= 0) {
          await processBatch<InputRow, OutputRow>(
            batch,
            errFormatStream,
            formatStream
          );
          batch = [];
        }
      });

    readStream.pipe(parseStream);

    formatStream.pipe(writeStream);
    errFormatStream.pipe(errWriteStream);

    writeStream.on("finish", resolve);
    errWriteStream.on("finish", resolve);
    writeStream.on("error", reject);
    errWriteStream.on("error", reject);
  });
};
