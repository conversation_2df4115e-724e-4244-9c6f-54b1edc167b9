import { HeaderArray } from "@fast-csv/parse";
import { MaterialItem } from "../../..";
import { addUnitPrice } from "../fillInfo/unitPrice";
import { convert } from "./cnCommon";
import { providerIdMap } from "./idMap";

export const headers = [
  "stockNumber", // 货号, A100110-2.5kg
  "SKU", // SKU, A100110
  "specification", // 包装, 2.5kg
  "nameZh", // 中文名, 五氧化二锑
  "nameEn", // 英文名, Antimony pentoxide
  "price", // 现价, 994.9
  "stock", // 库存, 6
  "purity", // 规格或纯度, 99%
  "CAS", // Cas编号, 1314-60-9
  "classification", // 分类, 试剂
  "controlInformation", // 管控信息, 一般危化品
  "stockTemperature", // 储存温度,
  "shippingConditions", // 运输条件, 常规运输
  "productCategory", // 产品类别, 化学和生化试剂
] as const;

export type AladingItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: AladingItem): MaterialItem => {
  const mid = {
    material_lib: providerIdMap.alading,

    ...convert(row),

    material_id: `aladin-${row.stockNumber}`,

    in_stock: Number.parseFloat(row.stock) > 0,
    max_delivery_days: undefined,
    min_delivery_days: undefined,
    purity: row.purity,

    source: "aladdin",
    source_link: `https://www.aladdin-e.com/zh_cn/${row.SKU}.html`,
    extension: undefined,
  };
  return addUnitPrice(mid);
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
