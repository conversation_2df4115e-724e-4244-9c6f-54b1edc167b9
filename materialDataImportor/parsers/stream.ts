import { FormatterRow, CsvParserStream, CsvFormatterStream } from "fast-csv";
import { MaterialItem } from "../..";
import { CsvMaterialItem } from "../utils";
import { handleRows } from "./batch";

export const handleStream = async (
  transform: (
    row: FormatterRow
  ) => Promise<Partial<MaterialItem> | false> | Partial<MaterialItem> | false,
  readSream: CsvParserStream<MaterialItem, MaterialItem>,
  outStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>,
  errStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>
) => {
  let validated = 0,
    notValidated = 0,
    skip = 0,
    total = 0;
  for await (const row of readSream) {
    const data = await transform(row);
    if (data) {
      const [vn, nvn, dn] = await handleRows(data, outStream, errStream);
      validated += vn;
      notValidated += nvn;
      skip += dn;
    } else {
      skip++;
    }
    total++;
    if (total % 10000 === 0) console.log(total);
  }

  const [vn, nvn, dn] = await handleRows("end", outStream, errStream);
  validated += vn;
  notValidated += nvn;
  skip += dn;

  console.log(
    `End: validated ${validated}, not validated ${notValidated} and skiped ${skip} rows in total ${total}`
  );
};
