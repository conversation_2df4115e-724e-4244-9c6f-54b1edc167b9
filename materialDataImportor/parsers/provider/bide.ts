import { MaterialItem } from "../../..";
import { HeaderArray } from "@fast-csv/parse";
import { convert } from "./cnCommon";
import { validateWithBool } from "../../utils";
import { addUnitPrice } from "../fillInfo/unitPrice";
import { providerIdMap } from "./idMap";

export const headers = [
  "brand", // 毕得医药
  "stockNumber", // BD0014
  "specification", // 1g
  "packNumber", // BD0014-1g
  "nameZh", // 4-(4-甲基哌嗪)苯甲醛
  "nameEn", // 4-(4-Methylpiperazin-1-yl)benzaldehyde
  "CAS", // 27913-99-1
  "MDL", // MFCD00452233
  "purity", // 97%
  "level", // AR
  "unit", // 瓶
  "stock", // 现货 / 期货
  "price", // 4620
  "priceWithDiscount", // 4620
  "link", // https://www.bidepharm.com/products/27913-99-1.html
] as const;

export type BideItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: BideItem): MaterialItem => {
  const mid = {
    material_lib: providerIdMap.bide,

    ...convert(row),

    material_id: `bide-${row.packNumber}`,

    in_stock: row.stock === "现货",
    max_delivery_days: undefined,
    min_delivery_days: undefined,
    purity: row.purity,

    source: "bidepharm",
    source_link: row.link,
    extension: undefined,
  };

  return addUnitPrice(mid);
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
