import { createReadStream } from "fs";
import { createGunzip } from "zlib";
import { extract } from "tar-stream";
import { ParsedInfo } from "..";
import { update } from "../../materialDataImportor/clickhouse";

export interface PubchemSqlData {
  id: number;
  data: {
    entry_term: string[];
    synonyms: string[];
    cas_like: string[];
    computed_properties: Record<string, string>;
    experimental_properties: Record<string, { origin: string }[]>;
  };
}

const parse = (data: ParsedInfo, id: number): PubchemSqlData => {
  return {
    id,
    data: {
      entry_term: data.entryTerm,
      synonyms: data.synonyms,
      cas_like: data.casLikes,
      computed_properties: data.computedProperties,
      experimental_properties: data.experimentalProperties,
    },
  };
};

const handleDataBatchly = async (batch: PubchemSqlData[]): Promise<void[]> => {
  return Promise.all(batch.map(update));
};

const batchSize = 100;
const processZip = async (path: string) => {
  let batch: PubchemSqlData[] = [];
  createReadStream(path)
    .pipe(createGunzip())
    .pipe(extract())
    .on("entry", (header, stream, next) => {
      // Check if the entry is a file and has a .json extension
      if (header.type === "file" && header.name.endsWith(".json")) {
        const fileName =
          header.name.split("/")[header.name.split("/").length - 1];
        const id = Number.parseInt(fileName.split(".")[0]);

        let fileContents = "";
        stream
          .on("data", (chunk) => (fileContents += chunk))
          .on("end", async () => {
            // Process the JSON file contents here
            const jsonData = parse(JSON.parse(fileContents), id);
            console.log(jsonData);
            batch.push(jsonData);
            if (batch.length >= batchSize) {
              await handleDataBatchly(batch);
              batch = [];
            }

            next(); // Proceed to the next entry
          });
      } else {
        stream.resume(); // Skip this entry
        next();
      }
    })
    .on("finish", () => {
      console.log("Finished processing the .tar.gz file");
    });
};

processZip("data/origin/material-data.tar.gz").catch(console.error);
