import { CsvFormatterStream } from "fast-csv";
import { MaterialItem } from "../..";
import { CsvMaterialItem, validateWithBool } from "../utils";
import { addInchiFiedSmiles } from "./fillInfo/inchifiedSmiles";
import { addMculePrice } from "./fillInfo/mculePrice";
import { addSmilesByCas } from "./fillInfo/smilesByCas";
import { addSmilesBySynonym } from "./fillInfo/smilesBySynonym";
import { round } from "lodash";
import { PriceInfo } from "./format/axios";
import { addUnifiedSmiles } from "./fillInfo/unifiedSmiles";
import { addUnitPrice } from "./fillInfo/unitPrice";
import { addCasAndName } from "./fillInfo/casAndName";
import { addCodes } from "./fillInfo/codes";

const batch: Partial<MaterialItem>[] = [];
const batchSize = 10000;

export const handleRows = async (
  row: Partial<MaterialItem> | "end",
  outStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>,
  errStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>
): Promise<[number, number, number]> => {
  if (row === "end") {
    return handleRowsInside(outStream, errStream);
  }
  batch.push(row);
  if (batch.length >= batchSize) {
    return handleRowsInside(outStream, errStream);
  }
  return [0, 0, 0];
};

const handleRowsInside = async (
  outStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>,
  errStream: CsvFormatterStream<MaterialItem, CsvMaterialItem>
): Promise<[number, number, number]> => {
  if (batch.length <= 0) return [0, 0, 0];
  const copied = batch.concat();
  batch.splice(0, batch.length);

  let dropedNum = 0;
  const [addPrices, toDrops] = await addMculePrice(copied);
  dropedNum += toDrops.length;
  const filteredNoPrices = addPrices.filter((p) => !!p.price);
  dropedNum += addPrices.length - filteredNoPrices.length;

  const added = await addSmilesBySynonym(filteredNoPrices.map(addUnitPrice))
    .then(addSmilesByCas)
    .then(addInchiFiedSmiles)
    // .then(addUnifiedSmiles)
    // .then(addCasAndName)
    .then(addCodes);
  const results = added.map(validateWithBool);

  let validatedNum = 0,
    notValidatedNum = 0;
  results.forEach(([validated, data]) => {
    validated ? validatedNum++ : notValidatedNum++;
    (validated ? outStream : errStream).write({
      ...data,
      price: round(data.price),
    });
  });
  return [validatedNum, notValidatedNum, dropedNum];
};

export const testBatch = async (rows: (MaterialItem & PriceInfo)[]) => {
  const a0 = await addSmilesBySynonym(rows.map(addUnitPrice));
  const a1 = await addSmilesByCas(a0);
  const a2 = await addInchiFiedSmiles(a1);
  const a3 = await addCasAndName(a2);
  const a4 = await addCodes(a3);
  return a4.map(validateWithBool);
};
