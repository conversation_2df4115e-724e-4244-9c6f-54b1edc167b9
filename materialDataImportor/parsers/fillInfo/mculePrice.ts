import { MaterialItem } from "../../..";
import { dollarToRmbRate } from "../../utils";
import { getMculeProviderInfoBatchly, PriceInfo } from "../format/axios";
import { addUnitPrice } from "./unitPrice";

export const addMculePrice = async (
  rows: Partial<MaterialItem>[]
): Promise<[(MaterialItem & PriceInfo)[], (MaterialItem & PriceInfo)[]]> => {
  const idsToGet = rows
    .filter((r) => r.material_lib == 4 && !r.price)
    .map((r) => r.material_id);

  if (idsToGet.length) {
    const prices = await getMculeProviderInfoBatchly(idsToGet);
    const updated = rows.map((r) => {
      const a = addUnitPrice({ ...r, ...prices[r.material_id] });
      delete a.priceIn$;
      return a;
    });
    return [
      updated.filter(
        (u) => u.unit_price <= 250 * dollarToRmbRate
      ) as (MaterialItem & PriceInfo)[],
      updated.filter(
        (u) => u.unit_price > 250 * dollarToRmbRate
      ) as (MaterialItem & PriceInfo)[],
    ];
  }
  return [rows as (MaterialItem & PriceInfo)[], []];
};
