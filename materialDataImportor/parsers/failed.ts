import { MaterialItem } from "../..";
import { getNumber } from "../utils";
import { HeaderArray } from "@fast-csv/parse";
import { calcUnitPrice } from "./fillInfo/unit";

export const headers = [
  "material_lib",
  "canonical_smiles",
  "inchified_smiles",
  "material_id",
  "cas_no",
  "name_en",
  "name_zh",
  "quantity",
  "unit",
  "price",
  "in_stock",
  "max_delivery_days",
  "min_delivery_days",
  "purity",
  "source",
  "source_link",
  "extension",
  "error",
] as const;

export type ErrorItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: ErrorItem): MaterialItem => {
  return {
    material_lib: getNumber(row.material_lib) || 0,
    canonical_smiles: row.canonical_smiles,
    inchified_smiles: row.inchified_smiles,
    material_id: row.material_id,
    cas_no: row.cas_no,
    name_en: row.name_en,
    name_zh: row.name_zh,
    quantity: getNumber(row.quantity) || 0,
    unit: row.unit,
    price: getNumber(row.price) || 0,
    ...calcUnitPrice(
      row.unit,
      getNumber(row.quantity) || 0,
      getNumber(row.price) || 0
    ),

    in_stock: !!row.in_stock,
    max_delivery_days: getNumber(row.max_delivery_days),
    min_delivery_days: getNumber(row.min_delivery_days),
    purity: row.purity,
    source: row.source,
    source_link: row.source_link,
    extension: row.extension,
  };
};

export const errorMain = async () => {
  // await transformCsv<ErrorItem, MaterialItem>(
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/leyan_err.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/leyan_err_out.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/leyan_err_err.csv',
  //   {
  //     headers: errorHeaders as unknown as string[],
  //     transform: errorTransformWithoutSmiles,
  //   },
  //   false,
  // );
  // await transformCsv<ErrorItem, MaterialItem>(
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/bide_err.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/bide_err_out.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/bide_err_err.csv',
  //   {
  //     headers: headers as unknown as string[],
  //     transform: transformWithoutSmiles,
  //   },
  //   false,
  // );
  // await transformCsv<ErrorItem, MaterialItem>(
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/alading_err.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/alading_err_out.csv',
  //   '/Users/<USER>/Work/brain-server/scripts/data/materials/alading_err_err.csv',
  //   {
  //     headers: headers as unknown as string[],
  //     transform: transformWithoutSmiles,
  //   },
  //   false,
  // );
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
