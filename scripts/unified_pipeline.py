#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据处理流水线

按顺序执行四个数据处理步骤：
1. convert_jsonl_to_csv.py - 将JSONL转换为CSV
2. update_csv_with_brain_api.py - 使用Brain API更新SMILES
3. update_csv_with_clickhouse.py - 使用ClickHouse更新hazard信息
4. deduplicate_and_update_prices.py - 去重和价格更新

用法:
    python3 unified_pipeline.py [options]
"""

import os
import sys
import argparse
import subprocess
import time
import psutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# 添加当前脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig

class PipelineMonitor:
    """流水线监控器"""
    
    def __init__(self):
        self.start_time = None
        self.step_times = {}
        self.step_start_time = None
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss
        self.peak_memory = self.initial_memory
        
    def start_pipeline(self):
        """开始流水线监控"""
        self.start_time = time.time()
        print(f"🚀 流水线开始执行 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 初始内存使用: {self.initial_memory / 1024 / 1024:.1f} MB")
        
    def start_step(self, step_name: str):
        """开始步骤监控"""
        self.step_start_time = time.time()
        current_memory = self.process.memory_info().rss
        self.peak_memory = max(self.peak_memory, current_memory)
        print(f"\n⏳ 开始执行: {step_name}")
        print(f"📊 当前内存使用: {current_memory / 1024 / 1024:.1f} MB")
        
    def end_step(self, step_name: str, success: bool = True):
        """结束步骤监控"""
        if self.step_start_time:
            duration = time.time() - self.step_start_time
            self.step_times[step_name] = duration
            current_memory = self.process.memory_info().rss
            self.peak_memory = max(self.peak_memory, current_memory)
            
            status = "✅ 完成" if success else "❌ 失败"
            print(f"{status}: {step_name} - 耗时: {duration:.1f}秒")
            print(f"📊 当前内存使用: {current_memory / 1024 / 1024:.1f} MB")
            
    def end_pipeline(self, success: bool = True):
        """结束流水线监控"""
        if self.start_time:
            total_duration = time.time() - self.start_time
            current_memory = self.process.memory_info().rss
            
            print(f"\n{'🎉' if success else '💥'} 流水线{'完成' if success else '失败'} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
            print(f"📊 峰值内存使用: {self.peak_memory / 1024 / 1024:.1f} MB")
            print(f"📊 内存增长: {(current_memory - self.initial_memory) / 1024 / 1024:.1f} MB")
            
            if self.step_times:
                print("\n📈 各步骤耗时统计:")
                for step, duration in self.step_times.items():
                    percentage = (duration / total_duration) * 100
                    print(f"  - {step}: {duration:.1f}秒 ({percentage:.1f}%)")

class UnifiedPipeline:
    """统一流水线执行器"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.monitor = PipelineMonitor()
        self.scripts_dir = Path(__file__).parent
        
    def run_script(self, script_name: str, args: list, step_name: str) -> bool:
        """运行单个脚本"""
        script_path = self.scripts_dir / script_name
        if not script_path.exists():
            print(f"❌ 脚本不存在: {script_path}")
            return False
            
        cmd = [sys.executable, str(script_path)] + args
        
        self.monitor.start_step(step_name)
        
        try:
            print(f"🔧 执行命令: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.config.step_timeout
            )
            
            if result.returncode == 0:
                print(f"📝 输出: {result.stdout.strip()}")
                self.monitor.end_step(step_name, True)
                return True
            else:
                print(f"❌ 错误: {result.stderr.strip()}")
                self.monitor.end_step(step_name, False)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 步骤超时: {step_name}")
            self.monitor.end_step(step_name, False)
            return False
        except Exception as e:
            print(f"💥 执行异常: {str(e)}")
            self.monitor.end_step(step_name, False)
            return False
    
    def prepare_directories(self):
        """准备输出目录"""
        for directory in [self.config.output_dir, self.config.temp_dir, self.config.log_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            
    def get_file_paths(self) -> Dict[str, Path]:
        """获取各步骤的文件路径"""
        return {
            'input_jsonl': self.config.input_file,
            'step1_csv': self.config.temp_dir / 'material_items.csv',
            'step1_errors': self.config.temp_dir / 'conversion_errors.log',
            'step1_warnings': self.config.temp_dir / 'conversion_warnings.log',
            'step2_csv': self.config.temp_dir / 'material_items_brain_updated.csv',
            'step2_errors': self.config.temp_dir / 'brain_errors.csv',
            'step3_csv': self.config.temp_dir / 'material_items_clickhouse_updated.csv',
            'step3_errors': self.config.temp_dir / 'clickhouse_errors.csv',
            'final_csv': self.config.output_dir / 'final_processed.csv',
            'duplicates_log': self.config.output_dir / 'duplicates.log',
            'warnings_csv': self.config.output_dir / 'warnings.csv'
        }
    
    def run(self) -> bool:
        """运行完整流水线"""
        self.monitor.start_pipeline()
        
        try:
            # 准备目录
            self.prepare_directories()
            
            # 获取文件路径
            paths = self.get_file_paths()
            
            # 检查输入文件
            if not paths['input_jsonl'].exists():
                print(f"❌ 输入文件不存在: {paths['input_jsonl']}")
                return False
            
            # 步骤1: JSONL转CSV
            if not self.run_step1(paths):
                return False
                
            # 步骤2: Brain API更新
            if not self.run_step2(paths):
                return False
                
            # 步骤3: ClickHouse更新
            if not self.run_step3(paths):
                return False
                
            # 步骤4: 去重和价格更新
            if not self.run_step4(paths):
                return False
            
            # 清理临时文件（如果配置允许）
            if self.config.cleanup_temp:
                self.cleanup_temp_files(paths)
            
            self.monitor.end_pipeline(True)
            self.print_final_summary(paths)
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断执行")
            self.monitor.end_pipeline(False)
            return False
        except Exception as e:
            print(f"\n💥 流水线执行失败: {str(e)}")
            self.monitor.end_pipeline(False)
            return False
    
    def run_step1(self, paths: Dict[str, Path]) -> bool:
        """步骤1: JSONL转CSV"""
        args = [
            str(paths['input_jsonl']),
            str(paths['step1_csv']),
            str(paths['step1_errors']),
            str(paths['step1_warnings'])
        ]
        return self.run_script('convert_jsonl_to_csv.py', args, 'JSONL转CSV')
    
    def run_step2(self, paths: Dict[str, Path]) -> bool:
        """步骤2: Brain API更新"""
        args = [
            str(paths['step1_csv']),
            str(paths['step2_csv']),
            str(paths['step2_errors']),
            '--batch-size', str(self.config.brain_api_batch_size)
        ]
        return self.run_script('update_csv_with_brain_api.py', args, 'Brain API更新')
    
    def run_step3(self, paths: Dict[str, Path]) -> bool:
        """步骤3: ClickHouse更新"""
        args = [
            str(paths['step2_csv']),
            str(paths['step3_csv']),
            str(paths['step3_errors']),
            '--clickhouse-host', self.config.clickhouse_host
        ]
        return self.run_script('update_csv_with_clickhouse.py', args, 'ClickHouse更新')
    
    def run_step4(self, paths: Dict[str, Path]) -> bool:
        """步骤4: 去重和价格更新"""
        args = [
            str(paths['step3_csv']),
            str(paths['final_csv']),
            str(paths['duplicates_log']),
            str(paths['warnings_csv'])
        ]
        return self.run_script('deduplicate_and_update_prices.py', args, '去重和价格更新')
    
    def cleanup_temp_files(self, paths: Dict[str, Path]):
        """清理临时文件"""
        temp_files = [
            paths['step1_csv'], paths['step2_csv'], paths['step3_csv']
        ]
        
        for temp_file in temp_files:
            if temp_file.exists():
                temp_file.unlink()
                print(f"🗑️ 已删除临时文件: {temp_file}")
    
    def print_final_summary(self, paths: Dict[str, Path]):
        """打印最终摘要"""
        print("\n📋 处理完成摘要:")
        print(f"  📁 输出目录: {self.config.output_dir}")
        print(f"  📄 最终数据文件: {paths['final_csv']}")
        print(f"  📝 重复记录日志: {paths['duplicates_log']}")
        print(f"  ⚠️ 警告文件: {paths['warnings_csv']}")
        
        # 统计文件大小
        if paths['final_csv'].exists():
            size_mb = paths['final_csv'].stat().st_size / 1024 / 1024
            print(f"  📊 最终文件大小: {size_mb:.1f} MB")
        
        # 统计错误文件
        error_files = [paths['step1_errors'], paths['step2_errors'], paths['step3_errors']]
        for i, error_file in enumerate(error_files, 1):
            if error_file.exists() and error_file.stat().st_size > 0:
                print(f"  ❌ 步骤{i}错误文件: {error_file}")

def main():
    parser = argparse.ArgumentParser(
        description="统一数据处理流水线",
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    parser.add_argument(
        'input_file',
        help='输入JSONL文件路径'
    )
    
    parser.add_argument(
        '--output-dir',
        default='./output',
        help='输出目录 (默认: ./output)'
    )
    
    parser.add_argument(
        '--temp-dir',
        default='./temp',
        help='临时文件目录 (默认: ./temp)'
    )
    
    parser.add_argument(
        '--config-file',
        help='配置文件路径 (可选)'
    )
    
    parser.add_argument(
        '--brain-api-batch-size',
        type=int,
        default=10000,
        help='Brain API批处理大小 (默认: 10000)'
    )
    
    parser.add_argument(
        '--clickhouse-host',
        default='localhost',
        help='ClickHouse主机地址 (默认: localhost)'
    )
    
    parser.add_argument(
        '--cleanup-temp',
        action='store_true',
        help='完成后清理临时文件'
    )
    
    parser.add_argument(
        '--step-timeout',
        type=int,
        default=3600,
        help='单步骤超时时间(秒) (默认: 3600)'
    )
    
    args = parser.parse_args()
    
    # 先创建基础配置
    config = PipelineConfig(input_file=Path(args.input_file))
    
    # 如果提供了配置文件，先加载配置文件
    if args.config_file:
        config.load_from_file(args.config_file)
    
    # 然后用命令行参数覆盖配置（只覆盖非默认值）
    if args.output_dir != './output':
        config.output_dir = Path(args.output_dir)
    if args.temp_dir != './temp':
        config.temp_dir = Path(args.temp_dir)
    if args.brain_api_batch_size != 10000:
        config.brain_api_batch_size = args.brain_api_batch_size
    if args.clickhouse_host != 'localhost':
        config.clickhouse_host = args.clickhouse_host
    if args.cleanup_temp:
        config.cleanup_temp = args.cleanup_temp
    if args.step_timeout != 3600:
        config.step_timeout = args.step_timeout
     
    # 验证配置
    print("🔍 验证配置...")
    if not config.validate():
        print("❌ 配置验证失败，流水线终止")
        sys.exit(1)
    print("✅ 配置验证通过")
    
    # 创建并运行流水线
    pipeline = UnifiedPipeline(config)
    success = pipeline.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()