import {
  testSize,
  getReadStreamFromXlsx,
  getWriteStreamWithPromise,
} from "../../utils";
import { FormatterRow, parse, ParserHeaderArray } from "fast-csv";
import { MaterialItem } from "../../..";
import { config as aladingConfig } from "../provider/alading";
import { config as bideConfig } from "../provider/bide";
import { config as leyanConfig } from "../provider/leyan";
import { handleStream } from "../stream";

export type CnProdiverType = "leyan" | "alading" | "bide";

interface ParserConfig<T extends FormatterRow = FormatterRow> {
  headers: ParserHeaderArray;
  transform: (row: T) => MaterialItem;
}

const configs: Record<CnProdiverType, ParserConfig> = {
  alading: aladingConfig as ParserConfig,
  bide: bideConfig as ParserConfig,
  leyan: leyanConfig as ParserConfig,
};

export const parseToCsv = async (
  type: CnProdiverType,
  inPath: string,
  outDir: string,
  version: string,
  testMode: boolean = true
) => {
  const [outStream, pipe, outP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}.csv`
  );
  const [errStream, errPipe, errP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}-err.csv`
  );
  const { headers, transform } = configs[type];

  const parseStream = (await getReadStreamFromXlsx(inPath)).pipe(
    parse<FormatterRow, MaterialItem>({
      headers: headers,
      renameHeaders: true,
      maxRows: testMode ? testSize : undefined,
    })
  );

  pipe();
  errPipe();
  await handleStream(transform, parseStream, outStream, errStream);
  outStream.end();
  errStream.end();

  return Promise.all([outP, errP]);
};
