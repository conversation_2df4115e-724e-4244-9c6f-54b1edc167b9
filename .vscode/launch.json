{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "调试 Brain Client Example",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/brain_server_client/brain_client_example.py",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/brain_server_client",
      "python": "${workspaceFolder}/brain_server_client/.venv/bin/python",
      "env": {
        "BRAIN_SERVER_URL": "https://staging.trail.labwise.cn/api",
        "BRAIN_SERVER_TOKEN": ""
      }
    },
     {
      "name": "Material data main",
      "program": "${workspaceFolder}/materialDataImportor/index.ts",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "/Users/<USER>/.yarn/bin/ts-node",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ],
      "cwd": "${workspaceFolder}",
      "type": "node"
    },
    {
      "name": "Pubchem parser main",
      "program": "${workspaceFolder}/pubchemParser/index.ts",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "/Users/<USER>/.yarn/bin/ts-node",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ],
      "cwd": "${workspaceFolder}",
      "type": "node"
    },
    {
      "name": "Import json to db",
      "program": "${workspaceFolder}/pubchemParser/jsonToDb/index.ts",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "runtimeExecutable": "/Users/<USER>/.yarn/bin/ts-node",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ],
      "cwd": "${workspaceFolder}",
      "type": "node"
    }
  ]
}
