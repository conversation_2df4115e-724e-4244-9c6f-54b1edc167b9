import { round } from "lodash";
import { MaterialItem } from "../../..";

const standableUnits = [
  "t",
  "kg",
  "g",
  "mg",
  "μg",
  "l",
  "ml",
  "μl",
  "nmol",
  "μmol",
  "mmol",
  "mol",
  "un",
  "kn",
  "mu",
  "u",
  "ku",
  "mm",
  "cm",
  "m",
] as const;
type StandableUnit = (typeof standableUnits)[number];

const knownUnits = [
  "mm/ml",
  "ea",
  "unit",
  "piece",
  "gal",
  "kit",
  "lbs",
  "pic",
  "pkg",
  "pair",
  "set",
  "tab",
  "μg/kit",
];

const unifyUnit: Record<StandableUnit, [StandableUnit, number]> = {
  t: ["g", 1e6],
  kg: ["g", 1e3],
  g: ["g", 1],
  mg: ["g", 1e-3],
  μg: ["g", 1e-6],
  l: ["ml", 1e3],
  ml: ["ml", 1],
  μl: ["ml", 1e-3],
  nmol: ["mmol", 1e-6],
  μmol: ["mmol", 1e-3],
  mmol: ["mmol", 1],
  mol: ["mmol", 1e3],
  un: ["un", 1],
  kn: ["un", 1e3],
  mu: ["u", 1e-3],
  u: ["u", 1],
  ku: ["u", 1e3],
  mm: ["m", 1e-3],
  cm: ["m", 1e-2],
  m: ["m", 1],
};

const calcUnitPrice = (
  unit: string,
  quantity: number,
  price: number
): Pick<MaterialItem, "unified_unit" | "unit_price" | "unit_quantity"> => {
  const lowcaseUnit = unit.toLowerCase();
  if (standableUnits.includes(lowcaseUnit as StandableUnit)) {
    const [unit, factor] = unifyUnit[lowcaseUnit];
    const unit_quantity = quantity * factor;
    return {
      unified_unit: unit,
      unit_price: round(price / unit_quantity, 2),
      unit_quantity,
    };
  }
  if (knownUnits.includes(lowcaseUnit)) {
    return {
      unified_unit: lowcaseUnit,
      unit_price: round(price / quantity, 2),
      unit_quantity: quantity,
    };
  }
  if (
    lowcaseUnit.endsWith("s") &&
    knownUnits.includes(lowcaseUnit.slice(0, -1))
  ) {
    const unified_unit = lowcaseUnit.slice(0, -1);
    return {
      unified_unit,
      unit_price: round(price / quantity, 2),
      unit_quantity: quantity,
    };
  }

  console.warn(`Unknow unit: ${unit}`);
  return {
    unified_unit: lowcaseUnit,
    unit_price: round(price / quantity, 2),
    unit_quantity: quantity,
  };
};

export const addUnitPrice = <
  T extends Pick<MaterialItem, "unit" | "quantity" | "price"> &
    Partial<Pick<MaterialItem, "unified_unit" | "unit_price" | "unit_quantity">>
>(
  row: T
): T & Pick<MaterialItem, "unified_unit" | "unit_price" | "unit_quantity"> => {
  if (!row.unit || !row.price || !row.quantity) {
    return { ...row, unified_unit: "", unit_price: 0, unit_quantity: 0 };
  }
  const unit = calcUnitPrice(row.unit, row.quantity, row.price);
  return { ...row, ...unit };
};
