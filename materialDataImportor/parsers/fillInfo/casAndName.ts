import { MaterialItem } from "../../..";
import { getInfoFromInchifiedSmilesBatch } from "../../clickhouse";

export const addCasAndName = async <
  T extends Partial<
    Pick<MaterialItem, "inchified_smiles" | "cas_no" | "name_en">
  >
>(
  rows: T[]
): Promise<(T & Pick<MaterialItem, "cas_no" | "name_en">)[]> => {
  const smilesToGet = rows
    .filter((r) => r.inchified_smiles && (!r.cas_no || !r.name_en))
    .map((r) => r.inchified_smiles);

  if (smilesToGet.length) {
    const infos = await getInfoFromInchifiedSmilesBatch(new Set(smilesToGet));

    return rows.map((r) => {
      if ((r.cas_no && r.name_en) || !r.inchified_smiles) return r;

      const result = infos[r.inchified_smiles];
      if (!result) {
        return r;
      }
      const { synonyms } = result;
      return {
        ...r,
        cas_no: r.cas_no || getCasFromSynonyms(synonyms),
        name_en: r.name_en || getNameEnFromSynonyms(synonyms),
      };
    });
  }
  return rows as (T &
    Pick<MaterialItem, "inchified_smiles" | "cas_no" | "name_en">)[];
};

const isCas = /\b[1-9]{1}[0-9]{1,5}-\d{2}-\d\b/;

export const getCasFromSynonyms = (synonyms: string[]): string => {
  return synonyms.find((s) => isCas.test(s));
};

export const getNameEnFromSynonyms = (synonyms: string[]): string => {
  return synonyms.find((s) => !isCas.test(s));
};
