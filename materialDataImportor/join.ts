import * as fs from "fs";
import * as zlib from "zlib";
import * as fastcsv from "fast-csv";
import { getReadStream, getWriteStream, validate } from "./utils";
import { groupBy, mergeWith, pick, omit } from "lodash";
import { transformWithoutSmiles } from "./parsers/provider/emolecule";
import { MaterialItem } from "..";

const readAll = async <
  InputRow extends fastcsv.FormatterRow,
  OutputRow extends MaterialItem
>(
  inStream: fastcsv.CsvParserStream<InputRow, OutputRow>,
  transform: (row: InputRow) => OutputRow
): Promise<OutputRow[]> => {
  return new Promise((resolve, reject) => {
    const data: OutputRow[] = [];
    inStream
      .on("data", (r) => data.push(transform(r)))
      .on("error", (e) => reject(e))
      .on("end", () => resolve(data));
  });
};

const joinDatasByKey = async <T extends Record<string, any>>(
  merge: (...data: T[]) => T,
  base: T[],
  getKey: (data: T) => string | number,
  ...inputs: T[][]
): Promise<T[]> => {
  const inputByKeys = inputs.map((input) => groupBy(input, getKey));
  return base.map((baseItem) => {
    return merge(
      baseItem,
      ...(inputByKeys
        .map((inputByKey) => inputByKey[getKey(baseItem)]?.[0])
        .filter((i) => !!i) as unknown as T[])
    );
  });
};

const merge = <T extends Record<never, never>>(base: T, ...datas: T[]): T => {
  const undefinedOrNullKeys = Object.entries(base)
    .filter(
      ([_, value]) => value === undefined || value === null || value === ""
    )
    .map(([key]) => key);
  const result = [...datas].reverse().reduce<T>((merged, obj) => {
    return mergeWith(merged, pick(obj, undefinedOrNullKeys), ignoreEmptyValue);
  }, {} as T);
  return { ...base, ...result };
};
const ignoreEmptyValue = (objValue: any, srcValue: any) =>
  isEmpty(srcValue) ? objValue : srcValue;
const isEmpty = (value: any): boolean =>
  value === undefined || value === null || value === "";

const joinInStreams = async <OutputRow extends MaterialItem>(
  outStream: fastcsv.CsvFormatterStream<OutputRow, OutputRow>,
  errStream: fastcsv.CsvFormatterStream<OutputRow, OutputRow>,
  baseInStream: fastcsv.CsvParserStream<OutputRow, OutputRow>,
  ...inStreams: fastcsv.CsvParserStream<OutputRow, OutputRow>[]
): Promise<void> => {
  const [base, ...datas] = await Promise.all(
    [baseInStream, ...inStreams].map((inStream) =>
      readAll(inStream, (r) => omit(r, "error"))
    )
  );

  const merged = await joinDatasByKey(
    merge,
    base,
    (i) => i.material_id,
    ...datas
  );

  merged.forEach((m) => {
    const validated = validate(m);
    if (validated.error) errStream.write(validated);
    else outStream.write(validated);
  });
};

export const joinMain = async (
  basePath: string,
  toMergePath: string[],
  outPath: string,
  errPath: string
) => {
  return new Promise(async (resolve, reject) => {
    const baseStream = getReadStream(basePath);
    const dataStreams = toMergePath.map((path) => getReadStream(path));
    const [errStream, pipeErr] = getWriteStream(errPath, resolve, reject);
    const [outStream, pipeOut] = getWriteStream(outPath, resolve, reject);

    await joinInStreams(outStream, errStream, baseStream, ...dataStreams);
    pipeErr();
    pipeOut();
  });
};

export const simpleJoinMain = async (
  toMergePath: string[],
  outPath: string,
  errPath: string
) => {
  return new Promise(async (resolve, reject) => {
    const dataStreams = toMergePath.map((path) => getReadStream(path));
    const [errStream, pipeErr] = getWriteStream(errPath, resolve, reject);
    const [outStream, pipeOut] = getWriteStream(outPath, resolve, reject);

    const datas = await Promise.all(
      dataStreams.map((inStream) => readAll(inStream, (r) => r))
    );

    const proceced = new Set<string>();
    datas.flat().forEach((m) => {
      const validated = validate(transformWithoutSmiles(m));
      if (proceced.has(validated.material_id)) return;
      proceced.add(validated.material_id);
      if (validated.error) errStream.write(validated);
      else outStream.write(validated);
    });
    pipeErr();
    pipeOut();
  });
};

export const splitZip = async (
  zipFilePath: string,
  outPath: string,
  errPath: string,
  batch: number = 1,
  batchSize: number = 1000_0000
) => {
  return new Promise(async (resolve, reject) => {
    const [errStream, pipeErr] = getWriteStream(errPath, resolve, reject);
    const [outStream, pipeOut] = getWriteStream(outPath, resolve, reject);

    let c = 0;
    const readStream = fs.createReadStream(zipFilePath);
    const parseStream = fastcsv
      .parse({
        headers: true,
        skipRows: batchSize * (batch - 1),
        maxRows: batchSize,
      })
      .on("data", (row) => {
        const validated = validate(transformWithoutSmiles(row));
        if (validated.error) errStream.write(validated);
        else outStream.write(validated);
        c++;
        if (c > batchSize) parseStream.destroy();
      })
      .on("end", () => {
        console.log("CSV file partially processed");
      });

    readStream.pipe(zlib.createGunzip()).pipe(parseStream);

    pipeErr();
    pipeOut();
  });
};
