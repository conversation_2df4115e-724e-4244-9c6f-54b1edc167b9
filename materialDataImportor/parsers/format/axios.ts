import axios from "axios";
import axiosRetry, { IAxiosRetryConfig } from "axios-retry";
import { assign, chunk, round } from "lodash";
import pLimit from "p-limit";
import { Readable } from "stream";
import zlib from "zlib";
import { MaterialItem } from "../../..";
import { dollarToRmbRate } from "../../utils";

const defaultRetryConfig: IAxiosRetryConfig = {
  retries: 3,
  retryCondition: () => true,
  onRetry: (c, e) => {
    console.log(`retry ${c} times...`);
    console.error(e);
  },
  retryDelay: axiosRetry.exponentialDelay,
};

const mculeToken = "f55e176167f440ccdfbecda1371a6fc7a5ac7366";

const limit = pLimit(100);

const mcule = axios.create({
  baseURL: "https://mcule.com/api/v1",
  headers: { Authorization: `Token ${mculeToken}` },
});
axiosRetry(mcule, { ...defaultRetryConfig, retries: 10 });

const smilesServer = axios.create({
  baseURL: "http://moltrek-dev.labwise.cn",
});
axiosRetry(smilesServer, { ...defaultRetryConfig, retries: 10 });

const unifyServer = axios.create({
  baseURL:
    "http://ec2-69-230-201-63.cn-northwest-1.compute.amazonaws.com.cn:18082",
});
axiosRetry(unifyServer, { ...defaultRetryConfig, retries: 10 });

type FileInfo = {
  lastUpdate: string;
  smileFileUrl: string;
};
type MculeFileInfo = { inStore: FileInfo; full: FileInfo };

const getMculeFiles = async (): Promise<MculeFileInfo> => {
  const { data } = await mcule.get("/database-files");
  const results = ((data?.results as any[])?.filter(
    (r) => r.id === 1 || r.id === 2
  ) || []) as any[];

  return results.reduce<MculeFileInfo>((acc, cur) => {
    let index = "";
    if (cur.id === 1 || cur.name === "Mcule Full") {
      index = "full";
    } else if (cur.id === 2 || cur.name === "Mcule In Stock") {
      index = "inStore";
    }
    if (!index) return acc;
    acc[index] = {
      lastUpdate: cur.last_updated,
      smileFileUrl: cur.files?.find((f) => f.file_type === "smi.gz")
        ?.download_url,
    };
    return acc;
  }, {} as MculeFileInfo);
};

export const getMculeDataAsStream = async (
  lastUpdate?: string,
  type: "inStore" | "full" = "inStore"
): Promise<[Readable, string]> => {
  const fileInfo = (await getMculeFiles())[type];
  if (lastUpdate && fileInfo.lastUpdate <= lastUpdate) {
    return;
  }
  const file = await axios.get(fileInfo.smileFileUrl, {
    responseType: "stream",
  });
  return [file.data.pipe(zlib.createGunzip()), fileInfo.lastUpdate];
};

export interface MculePrice {
  amount: number;
  currency: string;
  delivery_time_working_days: number;
  price: number;
  purity: number;
  unit: string;
}

interface MculeResult {
  best_prices: MculePrice[];
  mcule_id: string;
}

export const getPricesById = async (id: string): Promise<MculePrice[]> => {
  try {
    const { data: prices } = await limit(() =>
      mcule.get(`/compound/${id}/prices`)
    );
    if (!prices?.best_prices?.length) return [];
    return prices.best_prices;
  } catch (e) {
    console.warn(`get mcule price error`, e);
    return [];
  }
};

export const getMculeProviderInfo = async (id: string): Promise<PriceInfo> => {
  const prices = await getPricesById(id);
  return calcPrice(prices);
};

const maxBatchSize = 100;

export const getMculeProviderInfoBatchly = async (
  ids: string[]
): Promise<Record<string, PriceInfo>> => {
  const limit = pLimit(10);
  const requests = chunk(ids, maxBatchSize).map((ids) =>
    limit(() => getMculeProviderInfos(ids))
  );
  const results = await Promise.all(requests);
  return assign(results[0], ...results.slice(1));
};

export const getMculeProviderInfos = async (
  ids: string[]
): Promise<Record<string, PriceInfo>> => {
  if (ids.length > maxBatchSize) {
    console.error(
      `over ids length for get provides, max is ${maxBatchSize} get ${ids.length}`
    );
    return {};
  }
  try {
    const {
      data: { results },
    } = await limit(() =>
      mcule.post(`/compounds/`, {
        mcule_ids: ids,
        availability: false,
        components: false,
        properties: false,
        price_amounts: [1, 10, 1000],
      })
    );
    return (results as MculeResult[]).reduce<Record<string, PriceInfo>>(
      (acc, cur) => {
        acc[cur.mcule_id] = calcPrice(cur.best_prices);
        return acc;
      },
      {}
    );
  } catch (e) {
    console.warn(`get mcule prices batchly error`, e);
    return {};
  }
};

export type PriceInfo = Pick<
  MaterialItem,
  | "price"
  | "unit"
  | "max_delivery_days"
  | "min_delivery_days"
  | "quantity"
  | "in_stock"
  | "purity"
> & { priceIn$: number };

export const calcPrice = (prices: MculePrice[]): PriceInfo => {
  if (!prices.length) return { price: 0, unit: "", quantity: 0, priceIn$: 0 };
  const { currency, amount, delivery_time_working_days, price, purity, unit } =
    prices.sort((a, b) => {
      return a.price / a.amount - b.price / b.amount;
    })[0];

  if (currency !== "USD") {
    console.warn(currency);
  }

  return {
    purity: typeof purity === "number" ? `${purity}` : undefined,
    unit,
    price: round(price * (currency === "USD" ? dollarToRmbRate : 1), 2),
    priceIn$: price,
    quantity: amount,
    min_delivery_days: delivery_time_working_days,
  };
};

export const getInchifiedSmileses = async (
  smiles: string[]
): Promise<string[]> => {
  const toGet = smiles.filter((s) => s.length > 0);
  try {
    const data = await limit(() =>
      smilesServer.post("/smiles/inchi", { smiles: toGet })
    ).catch((e) => {
      console.warn(`err when inchi catch`, e);
    });
    console.debug(`get inchid for ${smiles.length} items`);

    return data ? data.data.smiles : [];
  } catch (e) {
    console.error(e);
    return [];
  }
};

export const getUnifiedSmileses = async (
  smiles: string[]
): Promise<string[]> => {
  const toGet = smiles.filter((s) => s.length > 0);
  try {
    const data = await limit(() =>
      unifyServer.post("/smiles/unify_salt", { smiles: toGet })
    ).catch((e) => {
      console.warn(`err when unify catch`, e);
    });
    console.debug(`get unified smiles for ${smiles.length} items`);

    return data ? data.data.smiles : [];
  } catch (e) {
    console.error(e);
    return [];
  }
};
