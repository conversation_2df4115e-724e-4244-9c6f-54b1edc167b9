export interface PubChemViewResponse {
  Record: Record;
}

export interface Record {
  RecordNumber: number;
  RecordTitle: string;
  RecordType: string;
  Reference: Reference[];
  Section: Section[];
}

export interface Reference {
  ANID: number;
  Description: string;
  IsToxnet: boolean;
  LicenseNote: string;
  LicenseURL: string;
  Name: string;
  ReferenceNumber: number;
  SourceID: string;
  SourceName: string;
  URL: string;
}

export interface Section {
  Description: string;
  DisplayControls: DisplayControls;
  Information: Information[];
  Section?: Section[];
  TOCHeading: string;
  URL: string;
}

export interface DisplayControls {
  CreateTable: CreateTable;
  HideThisSection?: boolean;
  ListType?: string;
  MoveToTop: boolean;
  ShowAtMost: number;
}

export interface CreateTable {
  ColumnContents: string[];
  ColumnHeadings?: string[];
  ColumnsFromNamedLists?: ColumnsFromNamedLists;
  FromInformationIn: string;
  NumberOfColumns: number;
}

export interface ColumnsFromNamedLists {
  Name: string[];
  UseNamesAsColumnHeadings: boolean;
}

export interface Information {
  Description?: string;
  Name?: string;
  Reference?: string[];
  ReferenceNumber: number;
  URL: string;
  Value: Value;
}

export interface Value {
  Binary?: string[];
  Boolean?: boolean[];
  DateISO8601?: string[];
  ExternalDataURL?: string[];
  ExternalTableName?: string;
  ExternalTableNumRows?: number;
  MimeType?: string;
  Number?: number[];
  StringWithMarkup?: StringWithMarkup[];
  Unit?: string;
}

export interface StringWithMarkup {
  Markup?: Markup[];
  String: string;
}

export interface Markup {
  Extra: string;
  Length: number;
  Start: number;
  Type: string;
  URL: string;
}
