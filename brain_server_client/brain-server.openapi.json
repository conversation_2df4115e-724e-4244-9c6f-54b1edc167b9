{"openapi": "3.0.1", "info": {"title": "brain-server", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/smiles/kekulize": {"post": {"summary": "kekulize", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"type": "array", "items": {"type": "object", "properties": {"[smiles]": {"type": "string"}}}}}, "required": ["role"]}, "example": {"role": [{"CCCC(=O)Nc1ccc(OCC(CBr)OC(C)=O)c(C(C)=O)c1": "main_reactant", "CCCC(=O)Nc1ccc(OCC(CNC(C)C)OC(C)=O)c(C(C)=O)c1": "product", "CC(C)N": "reactant", "O=C([O-])[O-]": "other_reagent", "[K+]": "other_reagent"}, {"NCC1=CC=C(N)C=C1": "main_reactant", "NC1=CC=C(CNC(=O)C2=CC=C(F)C=C2)C=C1": "product", "O=C(Cl)C1=CC=C(F)C=C1": "reactant", "CCN(CC)CC": "solvent", "ClCCl": "solvent"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/smiles/inchi": {"post": {"summary": "inchi", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"type": "array", "items": {"type": "object", "properties": {"[smiles]": {"type": "string"}}}}}, "required": ["role"]}, "example": {"role": [{"CCCC(=O)Nc1ccc(OCC(CBr)OC(C)=O)c(C(C)=O)c1": "main_reactant", "CCCC(=O)Nc1ccc(OCC(CNC(C)C)OC(C)=O)c(C(C)=O)c1": "product", "CC(C)N": "reactant", "O=C([O-])[O-]": "other_reagent", "[K+]": "other_reagent"}, {"NCC1=CC=C(N)C=C1": "main_reactant", "NC1=CC=C(CNC(=O)C2=CC=C(F)C=C2)C=C1": "product", "O=C(Cl)C1=CC=C(F)C=C1": "reactant", "CCN(CC)CC": "solvent", "ClCCl": "solvent"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/smiles/canonicalize": {"post": {"summary": "canonicalize", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"type": "array", "items": {"type": "object", "properties": {"[smiles]": {"type": "string"}}}}}, "required": ["role"]}, "example": {"role": [{"CCCC(=O)Nc1ccc(OCC(CBr)OC(C)=O)c(C(C)=O)c1": "main_reactant", "CCCC(=O)Nc1ccc(OCC(CNC(C)C)OC(C)=O)c(C(C)=O)c1": "product", "CC(C)N": "reactant", "O=C([O-])[O-]": "other_reagent", "[K+]": "other_reagent"}, {"NCC1=CC=C(N)C=C1": "main_reactant", "NC1=CC=C(CNC(=O)C2=CC=C(F)C=C2)C=C1": "product", "O=C(Cl)C1=CC=C(F)C=C1": "reactant", "CCN(CC)CC": "solvent", "ClCCl": "solvent"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/smiles/unify": {"post": {"summary": "unify", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"type": "array", "items": {"type": "object", "properties": {"[smiles]": {"type": "string"}}}}}, "required": ["role"]}, "example": {"role": [{"CCCC(=O)Nc1ccc(OCC(CBr)OC(C)=O)c(C(C)=O)c1": "main_reactant", "CCCC(=O)Nc1ccc(OCC(CNC(C)C)OC(C)=O)c(C(C)=O)c1": "product", "CC(C)N": "reactant", "O=C([O-])[O-]": "other_reagent", "[K+]": "other_reagent"}, {"NCC1=CC=C(N)C=C1": "main_reactant", "NC1=CC=C(CNC(=O)C2=CC=C(F)C=C2)C=C1": "product", "O=C(Cl)C1=CC=C(F)C=C1": "reactant", "CCN(CC)CC": "solvent", "ClCCl": "solvent"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}}, "components": {"schemas": {}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer"}}}, "servers": [], "security": [{"bearer": []}]}