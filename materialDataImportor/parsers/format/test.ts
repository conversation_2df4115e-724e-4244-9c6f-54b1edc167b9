import {
  testSize,
  getWriteStreamWithPromise,
  getReadStream,
  getMultipleWriteStreamWithPromise,
} from "../../utils";
import { handleStream } from "../stream";
import { MaterialItem } from "../../..";

export const parseToCsv = async (
  inPath: string,
  outDir: string,
  testMode: boolean = true
) => {
  const idToStreams = getMultipleWriteStreamWithPromise(outDir);
  const streams = Object.values(idToStreams);
  const [errStream, errPipe, errP] = getWriteStreamWithPromise(
    `${outDir}/divide-err.csv`
  );

  const [idleStream] = getWriteStreamWithPromise(`${outDir}/divide-idle.csv`);

  const parseStream = getReadStream(inPath, {
    headers: true,
    maxRows: testMode ? testSize : undefined,
  }).on("data", (item: MaterialItem) => {
    idToStreams[`${item.material_lib}`][0].write(item);
  });

  streams.forEach((s) => s[1]());
  errPipe();
  await handleStream(
    async (r: MaterialItem) => r,
    parseStream,
    idleStream,
    errStream
  );
  streams.forEach((s) => s[0].end());
  errStream.end();

  return Promise.all([...streams.map((s) => s[2]), errP]);
};
