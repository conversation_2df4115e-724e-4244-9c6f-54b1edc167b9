#!/bin/bash

# Brain Server Client Setup Script
# This script helps you set up the Poetry environment and install dependencies

set -e

echo "🧠 Brain Server Client Setup"
echo "==========================="

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "   curl -sSL https://install.python-poetry.org | python3 -"
    echo "   Or visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

echo "✅ Poetry found: $(poetry --version)"

# Navigate to the brain_server_client directory
cd "$(dirname "$0")"

echo "📁 Working directory: $(pwd)"

# Install dependencies
echo "📦 Installing dependencies..."
poetry install

echo "🔧 Setting up virtual environment..."
poetry env info

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Activate the virtual environment: poetry shell"
echo "   2. Set environment variables:"
echo "      export BRAIN_SERVER_URL='https://staging.trail.labwise.cn/api'"
echo "      export BRAIN_SERVER_TOKEN='your_token_here'"
echo "   3. Run the example: python brain_client_example.py"
echo "   4. Or use VS Code debugger with the 'Brain Client Example' configuration"
echo ""
echo "🐛 For debugging in VS Code:"
echo "   - Open the brain_server_client folder"
echo "   - Press F5 and select 'Brain Client Example'"
echo "   - Make sure to set your BRAIN_SERVER_TOKEN in the launch.json"