import * as fs from "fs";
import {
  CsvFormatterStream,
  CsvParserStream,
  format,
  parse,
  ParserOptionsArgs,
} from "fast-csv";
import { MaterialItem } from "..";
import { Workbook } from "exceljs";
import { Readable } from "stream";
import { providerIdMap } from "./parsers/provider/idMap";
import { mapKeys, mapValues } from "lodash";

type SpecData = {
  quantity: number;
  unit: string;
};

export const parseSpecification = (spec: string): SpecData => {
  // Split the specification string into parts based on the '*' character
  const parts = spec.split(/[*×]/).map((part) => part.trim());

  if (parts.length === 1) {
    // If there's no '*', just parse the quantity and unit directly
    const regex = /^(\d+(\.\d+)?)(\D+)$/;
    const match = parts[0].match(regex);

    if (match) {
      const quantity = parseFloat(match[1]);
      const unit = match[3];
      return { quantity, unit };
    } else if (parts[0].match(/^(\D+)$/)) {
      return { quantity: 1, unit: parts[0] };
    }
  } else if (parts.length === 2) {
    // If there's a '*', parse the two numeric values and the unit, and multiply the numeric values
    const regex1 = /^(\d+(\.\d+)?)$/;
    const regex2 = /^(\d+(\.\d+)?)(\D+)$/;
    const match1 = parts[0].match(regex1);
    const match2 = parts[1].match(regex2);
    const match3 = parts[0].match(regex2);
    const match4 = parts[1].match(regex1);

    if (match1 && match2) {
      const quantity1 = parseFloat(match1[1]);
      const quantity2 = parseFloat(match2[1]);
      const unit = match2[3];
      return { quantity: quantity1 * quantity2, unit };
    } else if (match3 && match4) {
      const quantity1 = parseFloat(match3[1]);
      const quantity2 = parseFloat(match4[1]);
      const unit = match3[3];
      return { quantity: quantity1 * quantity2, unit };
    }
  }

  return { quantity: 0, unit: "" }; // Return null if the spec string doesn't match the expected format
};

export const orderedHeaders = [
  "material_lib",
  "canonical_smiles",
  "inchified_smiles",
  "cas_no",
  "name_en",
  "name_zh",
  "quantity",
  "unit",
  "price",
  "material_id",
  "in_stock",
  "max_delivery_days",
  "min_delivery_days",
  "purity",
  "source",
  "source_link",
  "extension",
  "unified_unit",
  "unit_price",
  "unit_quantity",
  "codes",
  "pubchem_safety_link",
];

const requiredFields: (keyof MaterialItem)[] = [
  "material_lib",
  "canonical_smiles",
  "material_id",
  "quantity",
  "unit",
  // "unified_smiles",
  // 'price',
];
const notZeroFields: (keyof MaterialItem)[] = [
  "material_lib",
  "material_id",
  "quantity",
  "price",
];
export const validate = <T>(row: T): T & { error?: string } => {
  const lost = requiredFields
    .map((f) => {
      if (row[f] === undefined || row[f] === null || row[f] === "") {
        return f;
      }
      return "";
    })
    .filter((f) => !!f);
  if (lost.length) {
    return { ...row, error: `Lost fields: ${lost.join(", ")}` };
  }

  const zero = notZeroFields
    .map((f) => {
      if (row[f] === 0) return f;
    })
    .filter((f) => !!f);

  if (zero.length) {
    return { ...row, error: `zero fields: ${zero.join(", ")}` };
  }

  return row as T & { error?: string };
};

export const validateWithBool = <T>(
  row: T
): [boolean, T & { error?: string }] => {
  const result = validate(row);
  return [!("error" in result), result];
};

export const getNumber = (s: string): number | undefined => {
  const n = Number.parseFloat(s);
  if (Number.isNaN(n)) return undefined;
  return n;
};

export const getReadStream = (
  path: string,
  config: ParserOptionsArgs = {}
): CsvParserStream<any, any> => {
  const readStream = fs.createReadStream(path);
  const stream = parse({ headers: true, ...config });
  readStream.pipe(stream);
  return stream;
};

export const getReadStreamFromXlsx = async (
  path: string
): Promise<Readable> => {
  const book = new Workbook();
  await book.xlsx.readFile(path);

  const stream = new Readable({ read() {} });
  const buf = await book.csv.writeBuffer();
  stream.push(buf);
  stream.push(null);

  return stream;
};

export type CsvMaterialItem = Omit<MaterialItem, "codes"> & { codes: string };
const objectTransform = (row: MaterialItem): CsvMaterialItem => {
  return {
    ...row,
    codes: JSON.stringify(row.codes),
  };
};

export const getWriteStream = (
  path: string,
  resolve: (value: any) => void,
  reject: () => void
): [CsvFormatterStream<MaterialItem, CsvMaterialItem>, () => void] => {
  const writeStream = fs.createWriteStream(path);
  const outStream = format({
    headers: orderedHeaders,
    transform: objectTransform,
  });
  writeStream.on("finish", resolve);
  writeStream.on("error", reject);
  return [outStream, () => outStream.pipe(writeStream)];
};

export const getWriteStreamWithPromise = (
  path: string
): [
  CsvFormatterStream<MaterialItem, CsvMaterialItem>,
  () => void,
  Promise<boolean>
] => {
  const writeStream = fs.createWriteStream(path);
  const outStream = format({
    headers: orderedHeaders,
    transform: objectTransform,
  });
  const p = new Promise<boolean>((resolve) => {
    writeStream.on("finish", () => resolve(true));
    writeStream.on("error", () => resolve(false));
  });
  return [outStream, () => outStream.pipe(writeStream), p];
};

export const getMultipleWriteStreamWithPromise = (
  dir: string
): Record<
  string,
  [
    CsvFormatterStream<MaterialItem, CsvMaterialItem>,
    () => void,
    Promise<boolean>
  ]
> => {
  return mapValues(
    mapKeys(providerIdMap, (i) => i),
    (_, id) => {
      return getWriteStreamWithPromise(`${dir}/${id}.csv`);
    }
  );
};

export const testSize = 10000;

export const dollarToRmbRate = 7.1;
