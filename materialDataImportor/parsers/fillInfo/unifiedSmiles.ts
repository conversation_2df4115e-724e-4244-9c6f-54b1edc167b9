import { MaterialItem } from "../../..";
import { getUnifiedSmileses } from "../format/axios";

export const addUnifiedSmiles = async <
  T extends Partial<Pick<MaterialItem, "canonical_smiles" | "unified_smiles">>
>(
  rows: T[]
): Promise<(T & Pick<MaterialItem, "unified_smiles">)[]> => {
  const smilesToGet = rows
    .filter((r) => !r.unified_smiles && r.canonical_smiles)
    .map((r) => r.canonical_smiles);
  const smiles = [...new Set(smilesToGet)];

  if (smilesToGet.length) {
    const unified = await getUnifiedSmileses(smiles);
    const canoToUnified = smiles.reduce<Record<string, string>>(
      (acc, cur, index) => {
        acc[cur] = unified[index];
        return acc;
      },
      {}
    );
    return rows.map((r) => {
      if (r.unified_smiles)
        return r as T & Pick<MaterialItem, "unified_smiles">;
      return { ...r, unified_smiles: canoToUnified[r.canonical_smiles] };
    });
  }
  return rows as (T & Pick<MaterialItem, "unified_smiles">)[];
};
