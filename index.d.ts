/**
 * MaterialItem
 */
export interface MaterialItem {
  id?: number;
  material_lib_id: number;

  canonical_smiles: string;
  inchified_smiles: string;
  material_id: string;
  cas_no?: string;
  name_en?: string;
  name_zh?: string;

  quantity: number;
  unit: string;
  price: number;
  unified_unit: string;
  unit_price: number;
  unit_quantity: number;
  lowest_unit_price: boolean;
  in_stock?: boolean;
  max_delivery_days?: number;
  min_delivery_days?: number;
  purity?: string;

  source: string;
  source_link?: string;
  extension?: any;

  codes: string[];
  pubchem_safety_link: string;
  unified_smiles: string;
}

export type MaterialItemWithoutUnitPrice = Omit<
  MaterialItem,
  "unit_price" | "unified_unit" | "unit_quantity"
>;

export type CnMaterialItemBasic = Omit<
  MaterialItemWithoutUnitPrice,
  "canonical_smiles"
>;
