#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块化流水线测试脚本

用于测试和验证模块化流水线的功能
对比模块化流水线与原始分步执行的结果

用法:
    python3 test_modular_pipeline.py [测试数据文件]
"""

import os
import sys
import json
import time
import asyncio
import argparse
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any

# 添加scripts目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

from pipeline_config import PipelineConfig
from modular_pipeline import ModularPipeline

class ModularPipelineTest:
    """模块化流水线测试器"""
    
    def __init__(self, test_data_file: str, output_dir: str = None):
        self.test_data_file = Path(test_data_file)
        self.output_dir = Path(output_dir) if output_dir else Path("modular_test_results")
        self.scripts_dir = Path(__file__).parent / "scripts"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 子目录
        self.modular_output_dir = self.output_dir / "modular_pipeline"
        self.original_output_dir = self.output_dir / "original_pipeline"
        self.comparison_dir = self.output_dir / "comparison"
        
        for dir_path in [self.modular_output_dir, self.original_output_dir, self.comparison_dir]:
            dir_path.mkdir(exist_ok=True)
    
    async def run_modular_pipeline(self) -> Dict[str, Any]:
        """运行模块化流水线"""
        print("🔄 运行模块化流水线...")
        start_time = time.time()
        
        results = {
            "success": True,
            "total_time": 0,
            "error": None,
            "output_data": None,
            "error_records": {},
            "stats": {}
        }
        
        try:
            # 创建配置
            config = PipelineConfig(input_file=self.test_data_file)
            
            # 创建模块化流水线
            pipeline = ModularPipeline(config)
            
            # 运行流水线
            final_df, all_errors = await pipeline.run_pipeline(str(self.test_data_file))
            
            # 保存结果
            output_file = self.modular_output_dir / "final_output.csv"
            final_df.to_csv(output_file, index=False)
            
            # 保存错误记录
            errors_file = self.modular_output_dir / "all_errors.json"
            with open(errors_file, 'w', encoding='utf-8') as f:
                json.dump(all_errors, f, ensure_ascii=False, indent=2)
            
            results["output_data"] = final_df
            results["error_records"] = all_errors
            results["output_file"] = str(output_file)
            results["stats"] = {
                "total_rows": len(final_df),
                "total_errors": sum(len(errors) for errors in all_errors.values())
            }
            
            # 获取流水线统计信息
            pipeline.monitor.print_summary()
            
        except Exception as e:
            results["success"] = False
            results["error"] = str(e)
            print(f"❌ 模块化流水线执行失败: {e}")
        
        results["total_time"] = time.time() - start_time
        return results
    
    def run_original_pipeline_step(self, script_name: str, args: list, step_name: str) -> Tuple[bool, str]:
        """运行原始流水线的单个步骤"""
        import subprocess
        
        script_path = self.scripts_dir / script_name
        if not script_path.exists():
            return False, f"脚本不存在: {script_path}"
        
        cmd = [sys.executable, str(script_path)] + args
        
        try:
            print(f"🔧 执行: {step_name}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600
            )
            
            if result.returncode == 0:
                print(f"✅ {step_name} 完成")
                return True, result.stdout
            else:
                print(f"❌ {step_name} 失败: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            return False, f"步骤超时: {step_name}"
        except Exception as e:
            return False, f"执行异常: {str(e)}"
    
    def run_original_pipeline(self) -> Dict[str, Any]:
        """运行原始分步流水线"""
        print("🔄 运行原始分步流水线...")
        start_time = time.time()
        
        results = {
            "success": True,
            "steps": [],
            "total_time": 0,
            "error": None,
            "output_file": None
        }
        
        try:
            # 步骤1: convert_jsonl_to_csv.py
            csv_file = self.original_output_dir / "step1_output.csv"
            error_file = self.original_output_dir / "step1_errors.csv"
            warning_file = self.original_output_dir / "step1_warnings.csv"
            
            success, output = self.run_original_pipeline_step(
                "convert_jsonl_to_csv.py",
                [str(self.test_data_file), str(csv_file), str(error_file), str(warning_file)],
                "JSONL转CSV"
            )
            
            if not success:
                raise Exception(f"步骤1失败: {output}")
            
            # 步骤2: update_csv_with_brain_api.py
            brain_output = self.original_output_dir / "step2_output.csv"
            brain_error = self.original_output_dir / "step2_errors.csv"
            
            success, output = self.run_original_pipeline_step(
                "update_csv_with_brain_api.py",
                [str(csv_file), str(brain_output), str(brain_error), "--batch-size", "100"],
                "Brain API更新"
            )
            
            if not success:
                raise Exception(f"步骤2失败: {output}")
            
            # 步骤3: update_csv_with_clickhouse.py
            ch_output = self.original_output_dir / "step3_output.csv"
            ch_error = self.original_output_dir / "step3_errors.csv"
            
            success, output = self.run_original_pipeline_step(
                "update_csv_with_clickhouse.py",
                [str(brain_output), str(ch_output), str(ch_error)],
                "ClickHouse更新"
            )
            
            if not success:
                raise Exception(f"步骤3失败: {output}")
            
            # 步骤4: deduplicate_and_update_prices.py
            final_output = self.original_output_dir / "final_output.csv"
            dup_log = self.original_output_dir / "duplicates.log"
            warning_csv = self.original_output_dir / "warnings.csv"
            
            success, output = self.run_original_pipeline_step(
                "deduplicate_and_update_prices.py",
                [str(ch_output), str(final_output), str(dup_log), str(warning_csv)],
                "去重和价格更新"
            )
            
            if not success:
                raise Exception(f"步骤4失败: {output}")
            
            results["output_file"] = str(final_output)
            
        except Exception as e:
            results["success"] = False
            results["error"] = str(e)
            print(f"❌ 原始流水线执行失败: {e}")
        
        results["total_time"] = time.time() - start_time
        return results
    
    def compare_results(self, modular_results: Dict, original_results: Dict) -> Dict[str, Any]:
        """对比两个流水线的结果"""
        print("🔍 对比流水线结果...")
        
        comparison = {
            "both_successful": modular_results["success"] and original_results["success"],
            "performance_comparison": {
                "modular_time": modular_results["total_time"],
                "original_time": original_results["total_time"],
                "time_difference": modular_results["total_time"] - original_results["total_time"]
            },
            "data_comparison": None
        }
        
        # 如果两个流水线都成功，比较数据
        if comparison["both_successful"]:
            try:
                # 读取两个输出文件
                modular_df = modular_results["output_data"]
                original_df = pd.read_csv(original_results["output_file"])
                
                # 基本统计对比
                comparison["data_comparison"] = {
                    "modular_rows": len(modular_df),
                    "original_rows": len(original_df),
                    "row_difference": len(modular_df) - len(original_df),
                    "columns_match": list(modular_df.columns) == list(original_df.columns)
                }
                
                # 如果行数相同，进行详细对比
                if len(modular_df) == len(original_df) and comparison["data_comparison"]["columns_match"]:
                    try:
                        # 创建副本并处理包含列表的字段
                        modular_compare = modular_df.copy()
                        original_compare = original_df.copy()
                        
                        # 将可能包含列表的字段转换为字符串，并统一处理NaN和空值
                        for col in modular_compare.columns:
                            if modular_compare[col].dtype == 'object':
                                # 先填充NaN为空字符串，再转换为字符串
                                modular_compare[col] = modular_compare[col].fillna('').astype(str)
                                original_compare[col] = original_compare[col].fillna('').astype(str)
                                
                                # 将'nan'字符串也转换为空字符串，确保一致性
                                modular_compare[col] = modular_compare[col].replace('nan', '')
                                original_compare[col] = original_compare[col].replace('nan', '')
                        
                        # 尝试排序后对比，如果排序失败则直接对比
                        try:
                            # 使用稳定的列进行排序（避免包含列表的列）
                            sort_columns = ['id'] if 'id' in modular_compare.columns else [modular_compare.columns[0]]
                            modular_sorted = modular_compare.sort_values(by=sort_columns).reset_index(drop=True)
                            original_sorted = original_compare.sort_values(by=sort_columns).reset_index(drop=True)
                            
                            # 使用字符串转换后的数据进行对比
                            data_identical = modular_sorted.equals(original_sorted)
                            comparison["data_comparison"]["data_identical"] = data_identical
                        except Exception as sort_error:
                            print(f"⚠️ 排序失败，使用直接对比: {sort_error}")
                            # 如果排序失败，直接对比转换后的数据
                            data_identical = modular_compare.equals(original_compare)
                            comparison["data_comparison"]["data_identical"] = data_identical
                            print(f"🔍 直接对比结果: {data_identical}")
                        
                        # 如果字符串对比一致，但原始数据对比不一致，说明是数据类型问题
                        if comparison["data_comparison"]["data_identical"]:
                            try:
                                original_equals = modular_df.equals(original_df)
                                if not original_equals:
                                    comparison["data_comparison"]["note"] = "数据内容一致，但存在数据类型差异（如列表vs字符串）"
                            except:
                                pass
                    except Exception as sort_error:
                        print(f"⚠️ 数据排序对比失败: {sort_error}")
                        # 如果排序失败，尝试简单的行对行比较
                        try:
                            comparison["data_comparison"]["data_identical"] = modular_df.equals(original_df)
                        except Exception as equals_error:
                            print(f"⚠️ 数据直接对比也失败: {equals_error}")
                            comparison["data_comparison"]["data_identical"] = False
                            comparison["data_comparison"]["comparison_error"] = str(equals_error)
                else:
                    comparison["data_comparison"]["data_identical"] = False
                
            except Exception as e:
                comparison["data_comparison"] = {"error": str(e)}
        
        return comparison
    
    def generate_report(self, modular_results: Dict, original_results: Dict, comparison: Dict) -> str:
        """生成测试报告"""
        report_file = self.comparison_dir / "test_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 模块化流水线测试报告\n\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**测试数据**: {self.test_data_file}\n\n")
            
            # 执行结果
            f.write("## 执行结果\n\n")
            f.write(f"- **模块化流水线**: {'✅ 成功' if modular_results['success'] else '❌ 失败'}\n")
            f.write(f"- **原始流水线**: {'✅ 成功' if original_results['success'] else '❌ 失败'}\n\n")
            
            if not modular_results['success']:
                f.write(f"**模块化流水线错误**: {modular_results['error']}\n\n")
            
            if not original_results['success']:
                f.write(f"**原始流水线错误**: {original_results['error']}\n\n")
            
            # 性能对比
            f.write("## 性能对比\n\n")
            perf = comparison["performance_comparison"]
            f.write(f"- **模块化流水线耗时**: {perf['modular_time']:.2f} 秒\n")
            f.write(f"- **原始流水线耗时**: {perf['original_time']:.2f} 秒\n")
            f.write(f"- **时间差异**: {perf['time_difference']:.2f} 秒\n\n")
            
            # 数据对比
            if comparison["data_comparison"]:
                f.write("## 数据对比\n\n")
                data_comp = comparison["data_comparison"]
                
                if "error" in data_comp:
                    f.write(f"**对比错误**: {data_comp['error']}\n\n")
                else:
                    f.write(f"- **模块化流水线输出行数**: {data_comp['modular_rows']}\n")
                    f.write(f"- **原始流水线输出行数**: {data_comp['original_rows']}\n")
                    f.write(f"- **行数差异**: {data_comp['row_difference']}\n")
                    f.write(f"- **列结构匹配**: {'✅ 是' if data_comp['columns_match'] else '❌ 否'}\n")
                    
                    if "data_identical" in data_comp:
                        f.write(f"- **数据完全一致**: {'✅ 是' if data_comp['data_identical'] else '❌ 否'}\n")
                        
                        if "note" in data_comp:
                            f.write(f"- **说明**: {data_comp['note']}\n")
                        
                        if "comparison_error" in data_comp:
                            f.write(f"- **对比过程错误**: {data_comp['comparison_error']}\n")
                        
                        f.write("\n")
            
            # 总结
            f.write("## 测试总结\n\n")
            if comparison["both_successful"]:
                data_comp = comparison.get("data_comparison", {})
                if data_comp.get("data_identical", False):
                    if "note" in data_comp:
                        f.write("✅ **测试通过**: 模块化流水线与原始流水线数据内容完全一致\n")
                        f.write(f"📝 **说明**: {data_comp['note']}\n")
                    else:
                        f.write("✅ **测试通过**: 模块化流水线与原始流水线结果完全一致\n")
                else:
                    f.write("⚠️ **需要关注**: 两个流水线都成功执行，但输出结果存在差异\n")
            else:
                f.write("❌ **测试失败**: 至少有一个流水线执行失败\n")
        
        return str(report_file)
    
    async def run_test(self) -> bool:
        """执行完整的测试流程"""
        print(f"🚀 开始测试模块化流水线 - 测试数据: {self.test_data_file}")
        print(f"📁 输出目录: {self.output_dir}")
        
        # 运行模块化流水线
        modular_results = await self.run_modular_pipeline()
        
        # 运行原始流水线
        original_results = self.run_original_pipeline()
        
        # 对比结果
        comparison = self.compare_results(modular_results, original_results)
        
        # 生成报告
        report_file = self.generate_report(modular_results, original_results, comparison)
        
        print(f"\n📋 测试报告已生成: {report_file}")
        
        # 返回测试是否成功
        data_comp = comparison.get('data_comparison', {})
        success = (
            modular_results['success'] and 
            original_results['success'] and 
            data_comp.get('data_identical', False)
        )
        
        if success:
            if "note" in data_comp:
                print("✅ 测试成功: 模块化流水线与原始流水线数据内容完全一致")
                print(f"📝 说明: {data_comp['note']}")
            else:
                print("✅ 测试成功: 模块化流水线与原始流水线结果一致")
        else:
            print("⚠️ 测试需要关注: 请查看详细报告")
        
        return success

async def main():
    parser = argparse.ArgumentParser(description="测试模块化流水线与原始流水线的一致性")
    parser.add_argument("test_data", help="测试数据文件路径")
    parser.add_argument("--output-dir", help="测试结果输出目录", default="modular_test_results")
    
    args = parser.parse_args()
    
    # 检查测试数据文件
    if not os.path.exists(args.test_data):
        print(f"❌ 错误: 测试数据文件不存在: {args.test_data}")
        sys.exit(1)
    
    # 创建测试器并执行测试
    tester = ModularPipelineTest(args.test_data, args.output_dir)
    success = await tester.run_test()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())