import { MaterialItem } from "../../..";
import { HeaderArray } from "@fast-csv/parse";
import { providerIdMap } from "./idMap";

export const headers = ["smiles", "material_id"] as const;

export type MculeItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = async (
  row: MculeItem
): Promise<Partial<MaterialItem> | false> => {
  return {
    material_lib: providerIdMap.mcule,

    canonical_smiles: row.smiles,
    inchified_smiles: "",
    material_id: `${row.material_id}`,
    cas_no: "",
    name_en: "",
    name_zh: "",

    source: "mcule",
    source_link: `https://mcule.com/${row.material_id}`,
    extension: undefined,

    codes: [],
    pubchem_safety_link: "",
    unified_smiles: "",
  };
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
