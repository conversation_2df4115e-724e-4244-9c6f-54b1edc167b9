import { parseToCsv as fromXslx } from "./parsers/format/xslx";
import { parseToCsv as fromTsv } from "./parsers/format/tsv";
import { parseToCsv as fromMcule } from "./parsers/format/mcule";
import { parseToCsv as fromCsv } from "./parsers/format/csv";
import { parseToCsv as divideCsv } from "./parsers/format/test";
import { dbMain } from "./db";
import { Provider } from "./run";

const testMode = false;

const cnMain = async () => {
  await fromXslx(
    "alading",
    "data/origin/阿拉丁大数据2023.11.01 (1).xlsx",
    "data/out",
    "2023-11-01",
    testMode
  );

  await fromXslx(
    "bide",
    "data/origin/毕得通用-全目录(11-24-2023).xlsx",
    "data/out",
    "2023-11-24",
    testMode
  );

  await fromXslx(
    "leyan",
    "data/origin/乐研现货目录.xlsx",
    "data/out",
    "2023-11-01",
    testMode
  );
};

const emoleculeMain = async () => {
  await fromTsv(
    "emolecule",
    "data/origin/quote_bb.tsv",
    "data/out",
    "2023-11-01",
    testMode
  );
};

const mculeMain = async () => {
  await fromMcule("mcule", "data/out", "", testMode);
};

const divideMain = async () => {
  await divideCsv("data/out/all.csv", "data/out/divide", false);
};

const fixSingleMain = async (
  type: Provider,
  inPath: string,
  outDir: string,
  version: string,
  testMode: boolean = true
) => {
  await fromCsv(type, inPath, outDir, version, testMode);
  await dbMain(type, version, outDir, 1, true);
  console.info(`${type} end`);
};

const fixMain = async () => {
  await fixSingleMain(
    "alading",
    "data/out/final/alading-2023-10-09.csv",
    "data/out/fix",
    "2023-10-09",
    testMode
  );
  await fixSingleMain(
    "bide",
    "data/out/final/bide-2024-01-05.csv",
    "data/out/fix",
    "2024-01-05",
    testMode
  );
  await fixSingleMain(
    "leyan",
    "data/out/final/leyan-2023-09-02.csv",
    "data/out/fix",
    "2023-09-02",
    testMode
  );
  await fixSingleMain(
    "emolecule",
    "data/out/final/emolecule-2023-11-05.csv",
    "data/out/fix",
    "2023-11-05",
    testMode
  );
  await fixSingleMain(
    "mcule",
    "data/out/final/mcule-2023-09-16.csv",
    "data/out/fix",
    "2023-09-16",
    testMode
  );
};

const main = async () => {
  await fixMain();
};

main();
