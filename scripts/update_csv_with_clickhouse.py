#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV file update script - using ClickHouse

Updates the CSV file by filling in the codes and pubchem_safety_link columns based on the inchified_smiles column.
For rows where inchified_smiles is not found in the ClickHouse database, move the row to an error.csv file and add the reason for the error.

Usage:
    python3 update_csv_with_clickhouse.py input.csv output.csv error.csv
"""

import os
import csv
import argparse
import pandas as pd
import logging
from typing import Dict, Any, Optional, List, Tuple
from clickhouse_driver import Client


class CSVUpdater:
    def __init__(self, clickhouse_host: str = "localhost"):
        self.client = Client(host=clickhouse_host)
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0

    def query_hazards_batch(
        self, inchi_smis: list[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Query hazard information from ClickHouse for a batch of inchified_smiles.
        """
        if not inchi_smis:
            return {}

        # To avoid "Max query size exceeded", we split the query into smaller chunks.
        chunk_size = 1000
        all_results = {}
        for i in range(0, len(inchi_smis), chunk_size):
            chunk = inchi_smis[i:i + chunk_size]
            query = f"""
            SELECT inchi_smi, cid, hazards
            FROM default.pubchem_hazards
            WHERE inchi_smi IN {tuple(chunk)}
            """
            result = self.client.execute(query)
            for row in result:
                all_results[row[0]] = {"cid": row[1], "hazards": row[2]}
        return all_results

    def update_csv(
        self, input_file: str, output_file: str, error_file: str, batch_size: int = 10000
    ):
        """
        Update the CSV file.
        """
        print(f"Starting to process file: {input_file}")
        print(f"Output file: {output_file}")
        print(f"Error file: {error_file}")

        if not os.path.exists(input_file):
            print(f"Error: Input file not found: {input_file}")
            return

        with open(input_file, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames

        if not fieldnames:
            print("Error: Could not read CSV header")
            return

        if "inchified_smiles" not in fieldnames:
            print("Error: 'inchified_smiles' field is missing from the CSV file")
            return

        output_fieldnames = list(fieldnames)
        if "codes" not in output_fieldnames:
            output_fieldnames.append("codes")
        if "pubchem_safety_link" not in output_fieldnames:
            output_fieldnames.append("pubchem_safety_link")

        error_fieldnames = list(fieldnames)
        if "error_reason" not in error_fieldnames:
            error_fieldnames.append("error_reason")

        with (
            open(output_file, "w", newline="", encoding="utf-8") as success_f,
            open(error_file, "w", newline="", encoding="utf-8") as error_f,
        ):
            success_writer = csv.DictWriter(success_f, fieldnames=output_fieldnames)
            error_writer = csv.DictWriter(error_f, fieldnames=error_fieldnames)

            success_writer.writeheader()
            error_writer.writeheader()

            with open(input_file, "r", encoding="utf-8") as input_f:
                reader = csv.DictReader(input_f)
                batch = []
                for row in reader:
                    batch.append(row)
                    if len(batch) >= batch_size:
                        self._process_batch(
                            batch, success_writer, error_writer
                        )
                        batch = []

                if batch:
                    self._process_batch(batch, success_writer, error_writer)

        self._print_summary()
        self._verify_row_counts(input_file, output_file, error_file)

    def _process_batch(self, batch, success_writer, error_writer):
        self.processed_count += len(batch)
        unique_smiles = list(
            set(
                row.get("inchified_smiles", "").strip()
                for row in batch
                if row.get("inchified_smiles", "").strip()
            )
        )

        hazard_data = self.query_hazards_batch(unique_smiles)

        for row in batch:
            inchi_smi = row.get("inchified_smiles", "").strip()

            if not inchi_smi:
                row["error_reason"] = "inchified_smiles field is empty"
                error_writer.writerow(row)
                self.error_count += 1
                continue

            hazard_info = hazard_data.get(inchi_smi)

            if hazard_info:
                cid = hazard_info["cid"]
                hazards = hazard_info["hazards"]
                row["codes"] = hazards.split("|")
                row["pubchem_safety_link"] = (
                    f"https://pubchem.ncbi.nlm.nih.gov/compound/{cid}#section=Safety-and-Hazards"
                )
                success_writer.writerow(row)
                self.success_count += 1
            else:
                row["codes"] = []
                row["pubchem_safety_link"] = ""
                success_writer.writerow(row)
                self.warning_count += 1

        print(
            f"Processed: {self.processed_count} rows, Success: {self.success_count}, Error: {self.error_count}, Warning: {self.warning_count}"
        )

    def _print_summary(self):
        print(f"\nProcessing finished!")
        print(f"Total rows processed: {self.processed_count}")
        print(f"Successfully updated: {self.success_count}")
        print(f"Rows with warnings (not found in ClickHouse): {self.warning_count}")
        print(f"Error rows: {self.error_count}")
        if self.processed_count > 0:
            success_rate = self.success_count / self.processed_count * 100
            warning_rate = self.warning_count / self.processed_count * 100
            error_rate = self.error_count / self.processed_count * 100
            print(f"Success rate: {success_rate:.2f}%")
            print(f"Warning rate: {warning_rate:.2f}%")
            print(f"Error rate: {error_rate:.2f}%")
        else:
            print("Success rate: 0%")

    def _verify_row_counts(self, input_file: str, output_file: str, error_file: str):
        try:
            # Get line count, -1 for header
            with open(input_file, 'r', encoding='utf-8') as f:
                input_count = sum(1 for _ in f) - 1
            
            with open(output_file, 'r', encoding='utf-8') as f:
                # The output file contains successful and warning rows
                output_count = sum(1 for _ in f) - 1

            with open(error_file, 'r', encoding='utf-8') as f:
                error_count = sum(1 for _ in f) - 1

            print("\n--- File Row Count Verification ---")
            print(f"Original file rows: {input_count}")
            print(f"Output file rows (Success + Warnings): {output_count}")
            print(f"Error file rows: {error_count}")

            if input_count == output_count + error_count:
                print("✅ Verification Passed: Total row count matches.")
            else:
                print(f"❌ Verification Failed: Total row count mismatch! Difference: {input_count - (output_count + error_count)}")

        except FileNotFoundError as e:
            print(f"Row count verification failed: File not found {e.filename}")
        except Exception as e:
            print(f"An unknown error occurred during row count verification: {e}")
    
    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """处理DataFrame数据
        
        Args:
            df: 输入的DataFrame，必须包含inchified_smiles列
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (成功数据DataFrame, 错误数据DataFrame)
        """
        logger = logging.getLogger(__name__)
        
        # 检查必要字段
        if 'inchified_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少inchified_smiles字段")
        
        logger.info(f"开始处理DataFrame，共 {len(df)} 行")
        
        # 重置计数器
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0
        
        success_data = []
        error_data = []
        
        # 获取所有唯一的inchified_smiles
        unique_inchi_smis = df['inchified_smiles'].dropna().unique().tolist()
        
        if unique_inchi_smis:
            # 批量查询hazard信息
            hazards_dict = self.query_hazards_batch(unique_inchi_smis)
            logger.info(f"从ClickHouse查询到 {len(hazards_dict)} 条hazard信息")
        else:
            hazards_dict = {}
        
        # 处理每一行
        for idx, row in df.iterrows():
            row_dict = row.to_dict()
            inchi_smi = row_dict.get('inchified_smiles', '').strip()
            
            self.processed_count += 1
            
            if not inchi_smi:
                row_dict['error_reason'] = 'inchified_smiles字段为空'
                error_data.append(row_dict)
                self.error_count += 1
            elif inchi_smi in hazards_dict:
                # 找到匹配的hazard信息
                hazard_info = hazards_dict[inchi_smi]
                hazards = hazard_info['hazards']
                row_dict['codes'] = hazards.split('|') if hazards else []
                row_dict['pubchem_safety_link'] = f"https://pubchem.ncbi.nlm.nih.gov/compound/{hazard_info['cid']}#section=Safety-and-Hazards"
                success_data.append(row_dict)
                self.success_count += 1
            else:
                # 未找到匹配的hazard信息，作为警告处理
                row_dict['codes'] = []
                row_dict['pubchem_safety_link'] = ''
                success_data.append(row_dict)
                self.warning_count += 1
            
            # 进度报告
            if self.processed_count % 10000 == 0:
                logger.info(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
        
        # 创建结果DataFrame
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_df = pd.DataFrame(error_data) if error_data else pd.DataFrame()
        
        logger.info(f"处理完成: 总处理行数: {self.processed_count}, 成功: {self.success_count}, 错误: {self.error_count}")
        
        return success_df, error_df
    
    def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """更新DataFrame中的hazard信息
        
        Args:
            df: 输入的DataFrame，必须包含inchified_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"开始更新DataFrame中的hazard信息，共 {len(df)} 行")
        
        success_df, error_df = self.process_dataframe(df)
        
        # 转换错误数据为记录列表
        error_records = error_df.to_dict('records') if not error_df.empty else []
        
        return success_df, error_records


def main():
    parser = argparse.ArgumentParser(
        description="Update CSV file with data from ClickHouse based on inchified_smiles.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""Example:
  python3 update_csv_with_clickhouse.py data.csv
  python3 update_csv_with_clickhouse.py data.csv -o updated.csv -e errors.csv
  python3 update_csv_with_clickhouse.py data.csv updated.csv errors.csv --clickhouse-host 127.0.0.1""",
    )

    parser.add_argument("input_file", help="Input CSV file")
    parser.add_argument(
        "output_file",
        nargs="?",
        help="Output CSV file for successfully updated rows (optional, defaults to input_filename_updated.csv)",
    )
    parser.add_argument(
        "error_file",
        nargs="?",
        help="CSV file for rows with errors (optional, defaults to input_filename_errors.csv)",
    )
    parser.add_argument(
        "-o",
        "--output",
        dest="output_file_flag",
        help="Specify output file (overrides positional argument)",
    )
    parser.add_argument(
        "-e",
        "--error",
        dest="error_file_flag",
        help="Specify error file (overrides positional argument)",
    )
    parser.add_argument(
        "--clickhouse-host",
        default="ec2-161-189-187-79.cn-northwest-1.compute.amazonaws.com.cn",
        help="ClickHouse host (default: ec2-161-189-187-79.cn-northwest-1.compute.amazonaws.com.cn)",
    )
    parser.add_argument(
        "--version", action="version", version="CSV Updater with ClickHouse 1.0"
    )

    args = parser.parse_args()

    input_file = args.input_file

    input_dir = os.path.dirname(input_file)
    input_name, input_ext = os.path.splitext(os.path.basename(input_file))

    if args.output_file_flag:
        output_file = args.output_file_flag
    elif args.output_file:
        output_file = args.output_file
    else:
        output_file = os.path.join(input_dir, f"{input_name}_updated{input_ext}")

    if args.error_file_flag:
        error_file = args.error_file_flag
    elif args.error_file:
        error_file = args.error_file
    else:
        error_file = os.path.join(input_dir, f"{input_name}_errors{input_ext}")

    updater = CSVUpdater(clickhouse_host=args.clickhouse_host)

    try:
        updater.update_csv(input_file, output_file, error_file)
    except KeyboardInterrupt:
        print("\nUser interrupted processing")
    except Exception as e:
        print(f"\nAn error occurred during processing: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
