import { load } from "cheerio";
import { promises } from "fs";
import {
  parseEntryTerm,
  parseSynonyms,
  parseComputedProperties,
  parseExperimentalProperties,
} from "./parsers";

export const parseFile = async (filePath: string) => {
  const html = await promises.readFile(filePath, "utf-8");
  const $ = load(html);

  // 2.4.1
  const entryTerm = parseEntryTerm($);

  // 2.4.2
  const synonyms = parseSynonyms($);

  // 3.1
  const computedProperties = parseComputedProperties($);

  // 3.2
  const experimentalProperties = parseExperimentalProperties($);

  const parsed = {
    entryTerm,
    synonyms,
    computedProperties,
    experimentalProperties,
  };
  return parsed;
};
