#!/usr/bin/env python3
"""
Brain Server API Client

A Python utility to call brain-server APIs for SMILES processing.
Supports batch processing, deduplication, and parallel API calls.
"""

import asyncio
import aiohttp
from typing import List, Union, Dict, Optional
from dataclasses import dataclass
from collections import OrderedDict


DEFAULT_BASE = "https://staging.trail.labwise.cn/api"


@dataclass
class SmilesResult:
    """Result structure for processed SMILES"""

    canonical_smiles: Optional[str]
    inchified_smiles: Optional[str]


class BrainServerClient:
    """Client for brain-server API"""

    def __init__(
        self,
        base_url: str = DEFAULT_BASE,
        bearer_token: str = "",
        batch_size: int = 10000,
    ):
        """
        Initialize the client

        Args:
            base_url: Base URL of the brain-server API
            bearer_token: Bearer token for authentication
            batch_size: Maximum number of SMILES per API call (default: 10000)
        """
        self.base_url = base_url.rstrip("/")
        self.bearer_token = bearer_token
        self.batch_size = batch_size
        self.headers = {
            "Authorization": f"Bearer {bearer_token}",
            "Content-Type": "application/json",
        }

    async def _call_api(
        self, session: aiohttp.ClientSession, endpoint: str, smiles_list: List[str]
    ) -> Dict[str, Optional[str]]:
        """
        Call a specific API endpoint with a batch of SMILES

        Args:
            session: aiohttp session
            endpoint: API endpoint (e.g., 'canonicalize', 'inchi')
            smiles_list: List of SMILES strings

        Returns:
            Dictionary mapping input SMILES to output SMILES (None for invalid SMILES)
        """
        url = f"{self.base_url}/smiles/{endpoint}"

        # Prepare request body according to API specification
        payload = {"smiles": smiles_list}

        async with session.post(url, headers=self.headers, json=payload) as response:
            if response.status != 200:
                raise Exception(
                    f"API call failed with status {response.status}: {await response.text()}"
                )

            result = await response.json()

            # Parse the response based on the API structure: {smiles: string[]}
            if "smiles" not in result or not isinstance(result["smiles"], list):
                raise Exception(f"Invalid API response format: {result}")
            
            output_smiles_list = result["smiles"]
            
            # Check if input and output lengths match
            if len(output_smiles_list) != len(smiles_list):
                raise Exception(
                    f"API response length mismatch: input {len(smiles_list)}, output {len(output_smiles_list)}"
                )
            
            # Create mapping based on order correspondence
            smiles_mapping = {}
            for i, input_smiles in enumerate(smiles_list):
                output_smiles = output_smiles_list[i]
                # Check if API returned "CC" which indicates invalid SMILES
                if output_smiles == "CC":
                    smiles_mapping[input_smiles] = None
                else:
                    smiles_mapping[input_smiles] = output_smiles

            return smiles_mapping

    async def _process_batches(
        self, endpoint: str, unique_smiles: List[str]
    ) -> Dict[str, Optional[str]]:
        """
        Process SMILES in batches for a specific endpoint

        Args:
            endpoint: API endpoint
            unique_smiles: List of unique SMILES

        Returns:
            Dictionary mapping input SMILES to processed SMILES (None for invalid SMILES)
        """
        results = {}

        async with aiohttp.ClientSession() as session:
            # Process in batches sequentially for each endpoint
            for i in range(0, len(unique_smiles), self.batch_size):
                batch = unique_smiles[i : i + self.batch_size]
                batch_result = await self._call_api(session, endpoint, batch)
                results.update(batch_result)

        return results

    async def process_smiles(
        self, smiles_input: Union[str, List[str]]
    ) -> List[SmilesResult]:
        """
        Process SMILES to get canonical and inchified versions

        Args:
            smiles_input: Single SMILES string or list of SMILES strings

        Returns:
            List of SmilesResult objects in the same order as input
        """
        # Normalize input to list
        if isinstance(smiles_input, str):
            input_smiles = [smiles_input]
        else:
            input_smiles = list(smiles_input)

        # Create ordered dictionary to maintain input order while deduplicating
        unique_ordered = OrderedDict.fromkeys(input_smiles)
        unique_smiles = list(unique_ordered.keys())

        # Process both endpoints in parallel
        canonical_task = self._process_batches("canonicalize", unique_smiles)
        inchi_task = self._process_batches("inchi", unique_smiles)

        canonical_results, inchi_results = await asyncio.gather(
            canonical_task, inchi_task
        )

        # Build results in original input order
        results = []
        for smiles in input_smiles:
            # Check if SMILES was processed by both APIs
            if smiles not in canonical_results:
                raise Exception(f"SMILES '{smiles}' not found in canonical results")
            if smiles not in inchi_results:
                raise Exception(f"SMILES '{smiles}' not found in inchi results")
            
            canonical = canonical_results[smiles]
            inchified = inchi_results[smiles]

            results.append(
                SmilesResult(canonical_smiles=canonical, inchified_smiles=inchified)
            )

        return results

    def process_smiles_sync(
        self, smiles_input: Union[str, List[str]]
    ) -> List[SmilesResult]:
        """
        Synchronous wrapper for process_smiles

        Args:
            smiles_input: Single SMILES string or list of SMILES strings

        Returns:
            List of SmilesResult objects in the same order as input
        """
        return asyncio.run(self.process_smiles(smiles_input))


# Convenience function for easy usage
def process_smiles(
    smiles_input: Union[str, List[str]],
    base_url: str,
    bearer_token: str,
    batch_size: int = 10000,
) -> List[Dict[str, Optional[str]]]:
    """
    Convenience function to process SMILES

    Args:
        smiles_input: Single SMILES string or list of SMILES strings
        base_url: Base URL of the brain-server API
        bearer_token: Bearer token for authentication
        batch_size: Maximum number of SMILES per API call

    Returns:
        List of dictionaries with 'canonical_smiles' and 'inchified_smiles' keys (None for invalid SMILES)
    """
    client = BrainServerClient(base_url, bearer_token, batch_size)
    results = client.process_smiles_sync(smiles_input)

    return [
        {
            "canonical_smiles": result.canonical_smiles,
            "inchified_smiles": result.inchified_smiles,
        }
        for result in results
    ]


if __name__ == "__main__":
    # Example usage
    import os

    # Configuration - replace with actual values
    BASE_URL = os.getenv("BRAIN_SERVER_URL", DEFAULT_BASE)
    BEARER_TOKEN = os.getenv("BRAIN_SERVER_TOKEN", "your-bearer-token")

    # Example SMILES
    test_smiles = [
        "CCO",  # ethanol
        "CC(=O)O",  # acetic acid
        "CCO",  # duplicate ethanol
        "c1ccccc1",  # benzene
    ]

    try:
        results = process_smiles(test_smiles, BASE_URL, BEARER_TOKEN)

        print("Results:")
        for i, result in enumerate(results):
            print(f"{i + 1}. Input: {test_smiles[i]}")
            print(f"   Canonical: {result['canonical_smiles']}")
            print(f"   Inchified: {result['inchified_smiles']}")
            print()

    except Exception as e:
        print(f"Error: {e}")
        print("Please check your configuration and API credentials.")
