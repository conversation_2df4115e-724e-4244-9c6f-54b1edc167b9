import { CnProdiverType, parseToCsv as fromXslx } from "./parsers/format/xslx";
import {
  parseToCsv as fromTsv,
  ProdiverType as EmoProviderType,
} from "./parsers/format/tsv";
import {
  parseToCsv as fromMcule,
  ProdiverType as MculeProviderType,
} from "./parsers/format/mcule";
import { dbMain } from "./db";
import { getNumber } from "./utils";
import { existsSync } from "fs";

export type Provider = CnProdiverType | EmoProviderType | MculeProviderType;
const run = async (
  type: Provider,
  version: string,
  inFile: string,
  outPath: string,
  updator: string
) => {
  console.log(`Start run with ${type}, ${version}, ${inFile}, ${outPath}`);

  const outFile = `${outPath}/${type}-${version}.csv`;
  let newVersion = version;
  if (existsSync(outFile)) {
    console.log(`Out file already exist, skip parsing...`);
  } else {
    newVersion = await parseFile(type, inFile, outPath, version);
  }

  await dbMain(type, newVersion, outPath, getNumber(updator) || 1);
};

const parseFile = async (
  type: string,
  inFile: string,
  outPath: string,
  version: string
): Promise<string> => {
  switch (type) {
    case "alading":
    case "bide":
    case "leyan":
      await fromXslx(type, inFile, outPath, version, false);
      return version;
    case "emolecule":
      await fromTsv(type, inFile, outPath, version, false);
      return version;
    case "mcule":
      [version] = await fromMcule(type, outPath, "", false);
      return version;
  }
};

const args = process.argv.slice(2);
run(args[0] as Provider, args[1], args[2], args[3], args[4]);
