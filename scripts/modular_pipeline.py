#!/usr/bin/env python3
"""
模块化数据处理流水线
基于阶段一的配置，支持DataFrame流式处理
阶段三：批处理优化 - SMILES去重、并发处理、持久化缓存

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import pandas as pd
import time
import psutil
import os
import sys
import io
import pickle
import hashlib
from contextlib import redirect_stdout, redirect_stderr
from typing import Dict, List, Tuple, Set, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime, timedelta

# 导入阶段一的配置
from pipeline_config import PipelineConfig

# 导入各个处理模块
from convert_jsonl_to_csv import JSONLToCSVConverter
from update_csv_with_brain_api import CSVUpdater as BrainAPIUpdater
from update_csv_with_clickhouse import CSVUpdater as ClickHouseUpdater
from deduplicate_and_update_prices import deduplicate_and_update_dataframe


@dataclass
class CacheEntry:
    """缓存条目数据类"""
    data: Any
    timestamp: datetime
    ttl: int  # Time-to-live in seconds

    def is_expired(self) -> bool:
        """检查缓存是否过期"""
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl)


@dataclass
class SmilesResult:
    """SMILES处理结果数据类"""
    canonical_smiles: str
    inchified_smiles: str
    hazard_info: Optional[Dict[str, Any]] = None
    error_reason: Optional[str] = None


class CacheManager:
    """缓存管理器 - 支持内存和持久化缓存"""

    def __init__(self, cache_dir: str = "cache", default_ttl: int = 86400):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.default_ttl = default_ttl
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.stats = {
            'hits': 0,
            'misses': 0,
            'file_hits': 0,
            'file_misses': 0,
            'expired_entries': 0
        }

    def _get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长
        hash_key = hashlib.md5(cache_key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.pkl"

    def get(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        # 首先检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            if not entry.is_expired():
                self.stats['hits'] += 1
                return entry.data
            else:
                # 清理过期的内存缓存
                del self.memory_cache[cache_key]
                self.stats['expired_entries'] += 1

        # 检查文件缓存
        cache_file = self._get_cache_file_path(cache_key)
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    entry = pickle.load(f)
                if not entry.is_expired():
                    # 将文件缓存加载到内存缓存
                    self.memory_cache[cache_key] = entry
                    self.stats['file_hits'] += 1
                    return entry.data
                else:
                    # 删除过期的文件缓存
                    cache_file.unlink()
                    self.stats['expired_entries'] += 1
            except Exception as e:
                logging.getLogger(__name__).warning(f"读取缓存文件失败: {e}")
                # 删除损坏的缓存文件
                cache_file.unlink(missing_ok=True)

        self.stats['misses'] += 1
        return None

    def set(self, cache_key: str, data: Any, ttl: Optional[int] = None) -> None:
        """设置缓存数据"""
        if ttl is None:
            ttl = self.default_ttl

        entry = CacheEntry(data=data, timestamp=datetime.now(), ttl=ttl)

        # 设置内存缓存
        self.memory_cache[cache_key] = entry

        # 设置文件缓存
        cache_file = self._get_cache_file_path(cache_key)
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(entry, f)
        except Exception as e:
            logging.getLogger(__name__).warning(f"写入缓存文件失败: {e}")

    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0

        # 清理内存缓存
        expired_keys = [k for k, v in self.memory_cache.items() if v.is_expired()]
        for key in expired_keys:
            del self.memory_cache[key]
            cleaned_count += 1

        # 清理文件缓存
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                with open(cache_file, 'rb') as f:
                    entry = pickle.load(f)
                if entry.is_expired():
                    cache_file.unlink()
                    cleaned_count += 1
            except Exception:
                # 删除损坏的缓存文件
                cache_file.unlink(missing_ok=True)
                cleaned_count += 1

        self.stats['expired_entries'] += cleaned_count
        return cleaned_count

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            **self.stats,
            'memory_cache_size': len(self.memory_cache),
            'file_cache_size': len(list(self.cache_dir.glob("*.pkl"))),
            'hit_rate_percent': round(hit_rate, 2),
            'total_requests': total_requests
        }


class SmilesPreprocessor:
    """SMILES预处理器 - 支持去重、批处理和缓存"""

    def __init__(self, cache_dir: str = "cache", cache_ttl: int = 86400,
                 brain_batch_size: int = 10000, clickhouse_batch_size: int = 5000):
        self.cache_manager = CacheManager(cache_dir, cache_ttl)
        self.brain_batch_size = brain_batch_size
        self.clickhouse_batch_size = clickhouse_batch_size
        self.semaphore = asyncio.Semaphore(3)  # 最大3个并发请求
        self.logger = logging.getLogger(__name__)

        # 初始化处理器
        self.brain_updater = BrainAPIUpdater(batch_size=brain_batch_size)
        self.clickhouse_updater = ClickHouseUpdater()

    def collect_unique_smiles(self, jsonl_data: List[Dict[str, Any]]) -> Set[str]:
        """收集所有唯一的SMILES"""
        unique_smiles = set()
        for record in jsonl_data:
            canonical_smiles = record.get('canonical_smiles', '').strip()
            if canonical_smiles:
                unique_smiles.add(canonical_smiles)

        self.logger.info(f"收集到 {len(unique_smiles)} 个唯一SMILES")
        return unique_smiles

    async def batch_process_smiles(self, smiles_set: Set[str]) -> Dict[str, SmilesResult]:
        """批量处理SMILES - 使用Brain API和缓存"""
        results = {}
        smiles_list = list(smiles_set)

        # 首先检查缓存
        cached_results = {}
        uncached_smiles = []

        for smiles in smiles_list:
            cache_key = f"brain_api:{smiles}"
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                cached_results[smiles] = cached_result
            else:
                uncached_smiles.append(smiles)

        self.logger.info(f"缓存命中: {len(cached_results)}, 需要API处理: {len(uncached_smiles)}")

        # 批量处理未缓存的SMILES
        if uncached_smiles:
            api_results = await self._process_smiles_with_brain_api(uncached_smiles)

            # 缓存API结果
            for smiles, result in api_results.items():
                cache_key = f"brain_api:{smiles}"
                self.cache_manager.set(cache_key, result)

            results.update(api_results)

        # 合并缓存结果
        results.update(cached_results)

        return results

    async def _process_smiles_with_brain_api(self, smiles_list: List[str]) -> Dict[str, SmilesResult]:
        """使用Brain API处理SMILES列表"""
        results = {}

        # 分批处理以控制并发
        for i in range(0, len(smiles_list), self.brain_batch_size):
            batch = smiles_list[i:i + self.brain_batch_size]

            async with self.semaphore:  # 限制并发数
                try:
                    # 创建临时DataFrame进行处理
                    temp_df = pd.DataFrame({'canonical_smiles': batch})
                    success_df, error_records = await self.brain_updater.update_dataframe(temp_df)

                    # 处理成功结果
                    for _, row in success_df.iterrows():
                        smiles = row['canonical_smiles']
                        results[smiles] = SmilesResult(
                            canonical_smiles=row['canonical_smiles'],
                            inchified_smiles=row.get('inchified_smiles', '')
                        )

                    # 处理错误结果
                    for error_record in error_records:
                        smiles = error_record['canonical_smiles']
                        results[smiles] = SmilesResult(
                            canonical_smiles=smiles,
                            inchified_smiles='',
                            error_reason=error_record.get('error_reason', 'Unknown error')
                        )

                    self.logger.info(f"Brain API批次处理完成: {len(batch)} SMILES")

                except Exception as e:
                    self.logger.error(f"Brain API批次处理失败: {e}")
                    # 将整个批次标记为错误
                    for smiles in batch:
                        results[smiles] = SmilesResult(
                            canonical_smiles=smiles,
                            inchified_smiles='',
                            error_reason=f"API调用失败: {str(e)}"
                        )

        return results

    async def batch_query_hazards(self, smiles_set: Set[str]) -> Dict[str, Dict]:
        """批量查询hazard信息 - 使用ClickHouse和缓存"""
        results = {}
        smiles_list = list(smiles_set)

        # 首先检查缓存
        cached_results = {}
        uncached_smiles = []

        for smiles in smiles_list:
            cache_key = f"clickhouse_hazard:{smiles}"
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                cached_results[smiles] = cached_result
            else:
                uncached_smiles.append(smiles)

        self.logger.info(f"Hazard缓存命中: {len(cached_results)}, 需要查询: {len(uncached_smiles)}")

        # 批量查询未缓存的hazard信息
        if uncached_smiles:
            query_results = await self._query_hazards_from_clickhouse(uncached_smiles)

            # 缓存查询结果
            for smiles, result in query_results.items():
                cache_key = f"clickhouse_hazard:{smiles}"
                self.cache_manager.set(cache_key, result)

            results.update(query_results)

        # 合并缓存结果
        results.update(cached_results)

        return results

    async def _query_hazards_from_clickhouse(self, smiles_list: List[str]) -> Dict[str, Dict]:
        """从ClickHouse查询hazard信息"""
        results = {}

        # 分批查询以控制数据库负载
        for i in range(0, len(smiles_list), self.clickhouse_batch_size):
            batch = smiles_list[i:i + self.clickhouse_batch_size]

            async with self.semaphore:  # 限制并发数
                try:
                    # 创建临时DataFrame进行查询
                    temp_df = pd.DataFrame({'inchified_smiles': batch})
                    success_df, error_records = self.clickhouse_updater.update_dataframe(temp_df)

                    # 处理成功结果
                    for _, row in success_df.iterrows():
                        smiles = row['inchified_smiles']
                        results[smiles] = {
                            'codes': row.get('codes', []),
                            'pubchem_safety_link': row.get('pubchem_safety_link', '')
                        }

                    # 处理错误结果
                    for error_record in error_records:
                        smiles = error_record['inchified_smiles']
                        results[smiles] = {
                            'codes': [],
                            'pubchem_safety_link': '',
                            'error_reason': error_record.get('error_reason', 'Unknown error')
                        }

                    self.logger.info(f"ClickHouse批次查询完成: {len(batch)} SMILES")

                except Exception as e:
                    self.logger.error(f"ClickHouse批次查询失败: {e}")
                    # 将整个批次标记为错误
                    for smiles in batch:
                        results[smiles] = {
                            'codes': [],
                            'pubchem_safety_link': '',
                            'error_reason': f"数据库查询失败: {str(e)}"
                        }

        return results


@dataclass
class ModularPipelineStats:
    """模块化流水线统计信息"""

    total_start_time: float
    total_end_time: float
    step_times: Dict[str, float]
    memory_usage: Dict[str, float]
    data_counts: Dict[str, int]
    error_counts: Dict[str, int]

    @property
    def total_duration(self) -> float:
        return self.total_end_time - self.total_start_time

    def get_step_duration(self, step: str) -> float:
        return self.step_times.get(step, 0.0)


class ModularPipelineMonitor:
    """模块化流水线监控器"""

    def __init__(self):
        self.stats = ModularPipelineStats(
            total_start_time=0,
            total_end_time=0,
            step_times={},
            memory_usage={},
            data_counts={},
            error_counts={},
        )
        self.step_start_times = {}

    def start_pipeline(self):
        """开始流水线监控"""
        self.stats.total_start_time = time.time()
        self._log_memory_usage("pipeline_start")

    def end_pipeline(self):
        """结束流水线监控"""
        self.stats.total_end_time = time.time()
        self._log_memory_usage("pipeline_end")

    def start_step(self, step_name: str):
        """开始步骤监控"""
        self.step_start_times[step_name] = time.time()
        self._log_memory_usage(f"{step_name}_start")

    def end_step(self, step_name: str, data_count: int = 0, error_count: int = 0):
        """结束步骤监控"""
        if step_name in self.step_start_times:
            duration = time.time() - self.step_start_times[step_name]
            self.stats.step_times[step_name] = duration
            self.stats.data_counts[step_name] = data_count
            self.stats.error_counts[step_name] = error_count
            self._log_memory_usage(f"{step_name}_end")

    def _log_memory_usage(self, checkpoint: str):
        """记录内存使用情况"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.stats.memory_usage[checkpoint] = memory_mb

    def print_summary(self):
        """打印监控摘要"""
        print("\n" + "=" * 60)
        print("模块化流水线执行摘要")
        print("=" * 60)
        print(f"总执行时间: {self.stats.total_duration:.2f} 秒")
        print(
            f"内存使用: {self.stats.memory_usage.get('pipeline_start', 0):.1f} MB -> {self.stats.memory_usage.get('pipeline_end', 0):.1f} MB"
        )
        print("\n各步骤详情:")

        for step, duration in self.stats.step_times.items():
            data_count = self.stats.data_counts.get(step, 0)
            error_count = self.stats.error_counts.get(step, 0)
            success_rate = (
                ((data_count - error_count) / data_count * 100) if data_count > 0 else 0
            )
            print(
                f"  {step}: {duration:.2f}s | 数据: {data_count} | 错误: {error_count} | 成功率: {success_rate:.1f}%"
            )

        print("=" * 60)


class ModularPipeline:
    """模块化数据处理流水线 - 支持批处理优化"""

    def __init__(self, config: PipelineConfig):
        self.config = config
        self.monitor = ModularPipelineMonitor()
        self.logger = self._setup_logging()

        # 用于捕获子模块输出的缓冲区
        self.output_buffer = io.StringIO()
        self.error_buffer = io.StringIO()

        # 初始化各个处理器
        self.jsonl_converter = JSONLToCSVConverter()
        self.brain_updater = BrainAPIUpdater(batch_size=config.brain_api_batch_size)
        self.clickhouse_updater = ClickHouseUpdater(
            clickhouse_host=config.clickhouse_host
        )

        # 初始化SMILES预处理器（阶段三新增）
        cache_dir = str(config.temp_dir / "cache")
        self.smiles_preprocessor = SmilesPreprocessor(
            cache_dir=cache_dir,
            cache_ttl=86400,  # 24小时TTL
            brain_batch_size=config.brain_api_batch_size,
            clickhouse_batch_size=getattr(config, 'clickhouse_batch_size', 5000)
        )

        # 准备目录
        self.prepare_directories()

        # 配置子模块日志
        self._setup_submodule_logging()

    def prepare_directories(self):
        """准备输出目录"""
        for directory in [
            self.config.output_dir,
            self.config.temp_dir,
            self.config.log_dir,
        ]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_file_paths(self) -> Dict[str, str]:
        """获取各步骤的文件路径"""
        return {
            "step1_errors": str(self.config.temp_dir / "conversion_errors.log"),
            "step1_warnings": str(self.config.temp_dir / "conversion_warnings.log"),
            "step1_stats": str(self.config.temp_dir / "conversion_stats.log"),
            "step2_errors": str(self.config.temp_dir / "brain_errors.csv"),
            "step3_errors": str(self.config.temp_dir / "clickhouse_errors.csv"),
            "final_csv": str(self.config.output_dir / "final_processed.csv"),
            "duplicates_log": str(self.config.output_dir / "duplicates.log"),
            "warnings_csv": str(self.config.output_dir / "warnings.csv"),
            "temp_step1_csv": str(self.config.temp_dir / "material_items.csv"),
            "temp_step2_csv": str(
                self.config.temp_dir / "material_items_brain_updated.csv"
            ),
            "temp_step3_csv": str(
                self.config.temp_dir / "material_items_clickhouse_updated.csv"
            ),
            "pipeline_log": str(self.config.log_dir / "modular_pipeline.log"),
            "full_execution_log": str(self.config.log_dir / "full_execution.log"),
        }

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("modular_pipeline")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 清除现有的处理器以避免重复
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(self.config.log_format)
        
        # 控制台处理器 - 显示原始脚本的日志输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, self.config.log_level))
        logger.addHandler(console_handler)
        
        # 确保日志目录存在
        self.config.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器 - 保存所有运行日志到特定文件
        log_file_path = self.config.log_dir / "modular_pipeline.log"
        file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)  # 文件中记录所有级别的日志
        logger.addHandler(file_handler)
        
        # 完整执行日志处理器 - 包含详细的执行信息
        full_log_path = self.config.log_dir / "full_execution.log"
        full_handler = logging.FileHandler(full_log_path, mode='w', encoding='utf-8')
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        full_handler.setFormatter(detailed_formatter)
        full_handler.setLevel(logging.DEBUG)
        logger.addHandler(full_handler)
        
        # 记录日志设置信息
        logger.info(f"日志系统已初始化")
        logger.info(f"控制台日志级别: {self.config.log_level}")
        logger.info(f"文件日志保存到: {log_file_path}")
        logger.info(f"详细执行日志保存到: {full_log_path}")
        
        return logger
    
    def _setup_submodule_logging(self):
        """配置子模块的日志记录器"""
        # 获取子模块的日志记录器并配置它们
        submodule_loggers = [
            'convert_jsonl_to_csv',
            'update_csv_with_brain_api', 
            'update_csv_with_clickhouse',
            'deduplicate_and_update_prices'
        ]
        
        for logger_name in submodule_loggers:
            sublogger = logging.getLogger(logger_name)
            sublogger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            sublogger.handlers.clear()
            
            # 确保日志目录存在
            self.config.log_dir.mkdir(parents=True, exist_ok=True)
            
            # 添加文件处理器，将子模块日志也保存到主日志文件
            log_file_path = self.config.log_dir / "modular_pipeline.log"
            file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
            formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            sublogger.addHandler(file_handler)
            
            # 添加详细日志处理器
            full_log_path = self.config.log_dir / "full_execution.log"
            full_handler = logging.FileHandler(full_log_path, mode='a', encoding='utf-8')
            detailed_formatter = logging.Formatter(
                f'%(asctime)s - {logger_name} - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            full_handler.setFormatter(detailed_formatter)
            sublogger.addHandler(full_handler)
            
            # 设置传播，这样子模块的日志也会传播到根日志记录器
            sublogger.propagate = True
            
        self.logger.info("子模块日志记录器配置完成")
    
    def _log_system_info(self):
        """记录系统信息和配置"""
        import platform
        from datetime import datetime
        
        self.logger.info("=" * 80)
        self.logger.info("模块化数据处理流水线启动")
        self.logger.info("=" * 80)
        self.logger.info(f"启动时间: {datetime.now()}")
        self.logger.info(f"Python版本: {platform.python_version()}")
        self.logger.info(f"操作系统: {platform.system()} {platform.release()}")
        self.logger.info(f"处理器: {platform.processor()}")
        
        # 记录内存信息
        process = psutil.Process()
        memory_info = process.memory_info()
        self.logger.info(f"当前内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")
        
        # 记录配置信息
        self.logger.info("\n配置信息:")
        self.logger.info(f"  输入文件: {self.config.input_file}")
        self.logger.info(f"  输出目录: {self.config.output_dir}")
        self.logger.info(f"  临时目录: {self.config.temp_dir}")
        self.logger.info(f"  日志目录: {self.config.log_dir}")
        self.logger.info(f"  日志级别: {self.config.log_level}")
        self.logger.info(f"  Brain API批处理大小: {self.config.brain_api_batch_size}")
        self.logger.info(f"  ClickHouse主机: {self.config.clickhouse_host}")
        self.logger.info(f"  清理临时文件: {self.config.cleanup_temp}")
        self.logger.info(f"  步骤超时时间: {self.config.step_timeout}秒")
        self.logger.info("=" * 80)
    
    def _capture_and_log_output(self, func, *args, **kwargs):
        """捕获函数输出并记录到日志"""
        # 重置缓冲区
        self.output_buffer.seek(0)
        self.output_buffer.truncate(0)
        self.error_buffer.seek(0)
        self.error_buffer.truncate(0)
        
        try:
            # 捕获标准输出和标准错误
            with redirect_stdout(self.output_buffer), redirect_stderr(self.error_buffer):
                result = func(*args, **kwargs)
            
            # 获取捕获的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            # 将捕获的输出记录到日志并显示到控制台
            if stdout_content:
                for line in stdout_content.strip().split('\n'):
                    if line.strip():
                        print(line)  # 显示原始输出
                        self.logger.info(f"[子模块输出] {line}")
            
            if stderr_content:
                for line in stderr_content.strip().split('\n'):
                    if line.strip():
                        print(line, file=sys.stderr)  # 显示原始错误输出
                        self.logger.warning(f"[子模块错误] {line}")
            
            return result
            
        except Exception as e:
            # 记录异常信息
            self.logger.error(f"函数执行异常: {e}")
            # 获取可能的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            if stdout_content:
                self.logger.info(f"异常前的输出: {stdout_content}")
            if stderr_content:
                self.logger.error(f"异常前的错误: {stderr_content}")
            
            raise

    async def run_pipeline(
        self, input_jsonl_file: str
    ) -> Tuple[pd.DataFrame, Dict[str, List]]:
        """运行完整的模块化流水线

        Args:
            input_jsonl_file: 输入的JSONL文件路径

        Returns:
            Tuple[pd.DataFrame, Dict[str, List]]: (最终处理的DataFrame, 各步骤的错误记录)
        """
        self.monitor.start_pipeline()
        
        # 记录系统信息和配置
        self._log_system_info()
        
        self.logger.info(f"开始模块化流水线处理: {input_jsonl_file}")
        
        # 检查输入文件
        from pathlib import Path
        input_path = Path(input_jsonl_file)
        if not input_path.exists():
            self.logger.error(f"输入文件不存在: {input_jsonl_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_jsonl_file}")
        
        file_size_mb = input_path.stat().st_size / 1024 / 1024
        self.logger.info(f"输入文件大小: {file_size_mb:.1f} MB")

        # 获取文件路径
        paths = self.get_file_paths()

        all_errors = {
            "jsonl_conversion": [],
            "brain_api_update": [],
            "clickhouse_update": [],
            "deduplication": [],
        }

        try:
            # 步骤1: JSONL转换为DataFrame
            self.monitor.start_step("jsonl_conversion")
            self.logger.info("步骤1: 转换JSONL文件为DataFrame")
            self.logger.info(f"正在处理文件: {input_jsonl_file}")

            success_df, error_records, warning_records = self._capture_and_log_output(
                self.jsonl_converter.convert_jsonl_to_dataframe, input_jsonl_file, str(self.config.temp_dir)
            )
            all_errors["jsonl_conversion"].extend(error_records)
            all_errors["jsonl_conversion"].extend(warning_records)

            # 生成详细的统计报告
            self.logger.info("生成JSONL转换统计报告...")
            self._generate_conversion_stats_report(
                success_df, error_records, warning_records, paths["step1_stats"]
            )
            self.logger.info("统计报告生成完成")

            # 保存步骤1的中间结果和错误日志
            if not success_df.empty:
                self.logger.info("保存步骤1中间结果...")
                success_df.to_csv(paths["temp_step1_csv"], index=False)
                self.logger.info(f"步骤1中间结果已保存到: {paths['temp_step1_csv']}")

            self.logger.info("保存步骤1错误和警告日志...")
            self._save_step_errors(
                error_records, paths["step1_errors"], "conversion_errors"
            )
            self._save_step_errors(
                warning_records, paths["step1_warnings"], "conversion_warnings"
            )
            self.logger.info("步骤1日志保存完成")

            self.monitor.end_step(
                "jsonl_conversion",
                len(success_df),
                len(error_records) + len(warning_records),
            )
            self.logger.info(
                f"JSONL转换完成: 成功 {len(success_df)} 行, 错误/警告 {len(error_records) + len(warning_records)} 条"
            )

            if success_df.empty:
                self.logger.warning("JSONL转换后没有有效数据，流水线终止")
                return success_df, all_errors

            # 步骤2: Brain API更新SMILES
            self.monitor.start_step("brain_api_update")
            self.logger.info("步骤2: 使用Brain API更新SMILES")
            self.logger.info(f"待更新数据行数: {len(success_df)}")

            # 注意：对于异步函数，我们需要特殊处理
            self.logger.info("开始Brain API更新...")
            updated_df, brain_errors = await self.brain_updater.update_dataframe(
                success_df
            )
            self.logger.info("Brain API更新完成")
            all_errors["brain_api_update"].extend(brain_errors)

            # 保存步骤2的中间结果和错误日志
            if not updated_df.empty:
                self.logger.info("保存步骤2中间结果...")
                updated_df.to_csv(paths["temp_step2_csv"], index=False)
                self.logger.info(f"步骤2中间结果已保存到: {paths['temp_step2_csv']}")

            self.logger.info("保存步骤2错误日志...")
            self._save_step_errors(brain_errors, paths["step2_errors"], "brain_errors")
            self.logger.info("步骤2日志保存完成")

            self.monitor.end_step(
                "brain_api_update", len(updated_df), len(brain_errors)
            )
            self.logger.info(
                f"Brain API更新完成: 成功 {len(updated_df)} 行, 错误 {len(brain_errors)} 条"
            )

            # 步骤3: ClickHouse更新hazard信息
            self.monitor.start_step("clickhouse_update")
            self.logger.info("步骤3: 使用ClickHouse更新hazard信息")
            self.logger.info(f"待更新数据行数: {len(updated_df)}")

            hazard_updated_df, clickhouse_errors = self._capture_and_log_output(
                self.clickhouse_updater.update_dataframe, updated_df
            )
            all_errors["clickhouse_update"].extend(clickhouse_errors)

            # 保存步骤3的中间结果和错误日志
            if not hazard_updated_df.empty:
                self.logger.info("保存步骤3中间结果...")
                hazard_updated_df.to_csv(paths["temp_step3_csv"], index=False)
                self.logger.info(f"步骤3中间结果已保存到: {paths['temp_step3_csv']}")

            self.logger.info("保存步骤3错误日志...")
            self._save_step_errors(
                clickhouse_errors, paths["step3_errors"], "clickhouse_errors"
            )
            self.logger.info("步骤3日志保存完成")

            self.monitor.end_step(
                "clickhouse_update", len(hazard_updated_df), len(clickhouse_errors)
            )
            self.logger.info(
                f"ClickHouse更新完成: 成功 {len(hazard_updated_df)} 行, 错误 {len(clickhouse_errors)} 条"
            )

            # 步骤4: 去重和价格更新
            self.monitor.start_step("deduplication")
            self.logger.info("步骤4: 去重和价格更新")
            self.logger.info(f"去重前数据行数: {len(hazard_updated_df)}")

            final_df, dedup_records = self._capture_and_log_output(
                deduplicate_and_update_dataframe, hazard_updated_df
            )
            all_errors["deduplication"].extend(dedup_records)

            # 保存最终结果
            if not final_df.empty:
                self.logger.info("保存最终处理结果...")
                final_df.to_csv(paths["final_csv"], index=False)
                self.logger.info(f"最终结果已保存到: {paths['final_csv']}")

            # 保存去重日志
            self.logger.info("保存去重日志...")
            self._save_step_errors(dedup_records, paths["duplicates_log"], "duplicates")
            self.logger.info("步骤4日志保存完成")

            self.monitor.end_step("deduplication", len(final_df), len(dedup_records))
            self.logger.info(
                f"去重和价格更新完成: 最终 {len(final_df)} 行, 记录 {len(dedup_records)} 条"
            )

            return final_df, all_errors

        except Exception as e:
            self.logger.error(f"流水线执行出错: {e}")
            self.logger.exception("详细错误信息:")
            raise
        finally:
            self.monitor.end_pipeline()
            self.monitor.print_summary()
            self.print_final_summary(paths)
            
            # 记录最终统计信息
            self.logger.info("\n" + "=" * 80)
            self.logger.info("流水线执行完成")
            self.logger.info(f"总执行时间: {self.monitor.stats.total_duration:.2f} 秒")
            self.logger.info(f"最终内存使用: {self.monitor.stats.memory_usage.get('pipeline_end', 0):.1f} MB")
            self.logger.info("所有日志已保存到文件")
            self.logger.info("=" * 80)

    async def run_optimized_pipeline(
        self, input_jsonl_file: str
    ) -> Tuple[pd.DataFrame, Dict[str, List]]:
        """运行优化的两遍扫描流水线（阶段三新增）

        Args:
            input_jsonl_file: 输入的JSONL文件路径

        Returns:
            Tuple[pd.DataFrame, Dict[str, List]]: (最终处理的DataFrame, 各步骤的错误记录)
        """
        self.monitor.start_pipeline()

        # 记录系统信息和配置
        self._log_system_info()

        self.logger.info(f"开始优化流水线处理: {input_jsonl_file}")
        self.logger.info("使用两遍扫描优化策略")

        # 检查输入文件
        from pathlib import Path
        input_path = Path(input_jsonl_file)
        if not input_path.exists():
            self.logger.error(f"输入文件不存在: {input_jsonl_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_jsonl_file}")

        file_size_mb = input_path.stat().st_size / 1024 / 1024
        self.logger.info(f"输入文件大小: {file_size_mb:.1f} MB")

        # 获取文件路径
        paths = self.get_file_paths()

        all_errors = {
            "jsonl_conversion": [],
            "smiles_preprocessing": [],
            "brain_api_update": [],
            "clickhouse_update": [],
            "deduplication": [],
        }

        try:
            # 第一遍扫描：收集唯一SMILES
            self.monitor.start_step("first_pass_scan")
            self.logger.info("第一遍扫描: 收集唯一SMILES")

            # 转换JSONL为DataFrame
            success_df, error_records, warning_records = self._capture_and_log_output(
                self.jsonl_converter.convert_jsonl_to_dataframe, input_jsonl_file, str(self.config.temp_dir)
            )
            all_errors["jsonl_conversion"].extend(error_records)
            all_errors["jsonl_conversion"].extend(warning_records)

            if success_df.empty:
                self.logger.warning("JSONL转换后没有有效数据，流水线终止")
                return success_df, all_errors

            # 收集唯一SMILES
            jsonl_data = success_df.to_dict('records')
            unique_smiles = self.smiles_preprocessor.collect_unique_smiles(jsonl_data)

            self.monitor.end_step("first_pass_scan", len(success_df), len(error_records))
            self.logger.info(f"第一遍扫描完成: 数据行数 {len(success_df)}, 唯一SMILES {len(unique_smiles)}")

            # 批量预处理：并行调用Brain API和ClickHouse
            self.monitor.start_step("batch_preprocessing")
            self.logger.info("批量预处理: 并行处理SMILES和hazard信息")

            # 清理过期缓存
            cleaned_count = self.smiles_preprocessor.cache_manager.cleanup_expired()
            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个过期缓存条目")

            # 并行处理SMILES和hazard信息
            smiles_results_task = asyncio.create_task(
                self.smiles_preprocessor.batch_process_smiles(unique_smiles)
            )
            hazard_results_task = asyncio.create_task(
                self.smiles_preprocessor.batch_query_hazards(unique_smiles)
            )

            # 等待两个任务完成
            smiles_results, hazard_results = await asyncio.gather(
                smiles_results_task, hazard_results_task, return_exceptions=True
            )

            # 处理异常
            if isinstance(smiles_results, Exception):
                self.logger.error(f"SMILES批量处理失败: {smiles_results}")
                smiles_results = {}
                all_errors["smiles_preprocessing"].append({
                    'error': str(smiles_results),
                    'type': 'batch_smiles_processing'
                })

            if isinstance(hazard_results, Exception):
                self.logger.error(f"Hazard批量查询失败: {hazard_results}")
                hazard_results = {}
                all_errors["smiles_preprocessing"].append({
                    'error': str(hazard_results),
                    'type': 'batch_hazard_query'
                })

            self.monitor.end_step("batch_preprocessing", len(unique_smiles),
                                len(all_errors["smiles_preprocessing"]))

            # 打印缓存统计
            cache_stats = self.smiles_preprocessor.cache_manager.get_stats()
            self.logger.info(f"缓存统计: {cache_stats}")

            self.logger.info(f"批量预处理完成: SMILES结果 {len(smiles_results)}, Hazard结果 {len(hazard_results)}")

            # 第二遍处理：使用缓存数据进行转换
            self.monitor.start_step("second_pass_processing")
            self.logger.info("第二遍处理: 使用预处理结果更新数据")

            final_df = await self._apply_preprocessed_results(
                success_df, smiles_results, hazard_results, all_errors
            )

            self.monitor.end_step("second_pass_processing", len(final_df),
                                len(all_errors["brain_api_update"]) + len(all_errors["clickhouse_update"]))

            # 去重和价格计算
            self.monitor.start_step("deduplication")
            self.logger.info("步骤: 去重和价格更新")
            self.logger.info(f"去重前数据行数: {len(final_df)}")

            deduped_df, dedup_records = self._capture_and_log_output(
                deduplicate_and_update_dataframe, final_df
            )
            all_errors["deduplication"].extend(dedup_records)

            # 保存最终结果
            if not deduped_df.empty:
                self.logger.info("保存最终处理结果...")
                deduped_df.to_csv(paths["final_csv"], index=False)
                self.logger.info(f"最终结果已保存到: {paths['final_csv']}")

            self.monitor.end_step("deduplication", len(deduped_df), len(dedup_records))
            self.logger.info(f"去重完成: 最终数据行数 {len(deduped_df)}")

        except Exception as e:
            self.logger.error(f"优化流水线执行失败: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

        finally:
            self.monitor.end_pipeline()

            # 生成最终报告
            self._generate_final_report(paths, all_errors)

            # 打印监控摘要
            self.monitor.print_summary()

        return deduped_df, all_errors

    async def _apply_preprocessed_results(
        self,
        df: pd.DataFrame,
        smiles_results: Dict[str, SmilesResult],
        hazard_results: Dict[str, Dict],
        all_errors: Dict[str, List]
    ) -> pd.DataFrame:
        """应用预处理结果到DataFrame"""
        self.logger.info("应用预处理结果到数据")

        updated_data = []

        for _, row in df.iterrows():
            row_dict = row.to_dict()
            canonical_smiles = row_dict.get('canonical_smiles', '').strip()

            # 应用SMILES处理结果
            if canonical_smiles in smiles_results:
                smiles_result = smiles_results[canonical_smiles]
                if smiles_result.error_reason:
                    # 记录错误但继续处理
                    all_errors["brain_api_update"].append({
                        'canonical_smiles': canonical_smiles,
                        'error_reason': smiles_result.error_reason
                    })
                    # 保持原始值
                    row_dict['inchified_smiles'] = row_dict.get('inchified_smiles', '')
                else:
                    # 更新SMILES信息
                    row_dict['canonical_smiles'] = smiles_result.canonical_smiles
                    row_dict['inchified_smiles'] = smiles_result.inchified_smiles

            # 应用hazard查询结果
            inchified_smiles = row_dict.get('inchified_smiles', '').strip()
            if inchified_smiles in hazard_results:
                hazard_result = hazard_results[inchified_smiles]
                if 'error_reason' in hazard_result:
                    # 记录错误但继续处理
                    all_errors["clickhouse_update"].append({
                        'inchified_smiles': inchified_smiles,
                        'error_reason': hazard_result['error_reason']
                    })
                    # 设置默认值
                    row_dict['codes'] = []
                    row_dict['pubchem_safety_link'] = ''
                else:
                    # 更新hazard信息
                    row_dict['codes'] = hazard_result.get('codes', [])
                    row_dict['pubchem_safety_link'] = hazard_result.get('pubchem_safety_link', '')
            else:
                # 未找到hazard信息，设置默认值
                row_dict['codes'] = []
                row_dict['pubchem_safety_link'] = ''

            updated_data.append(row_dict)

        result_df = pd.DataFrame(updated_data)
        self.logger.info(f"预处理结果应用完成: {len(result_df)} 行数据")

        return result_df

    def _generate_final_report(self, paths: Dict[str, str], all_errors: Dict[str, List]):
        """生成最终处理报告"""
        try:
            report_path = str(self.config.output_dir / "processing_report.txt")

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"数据处理流水线报告\n")
                f.write(f"生成时间: {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")

                # 缓存统计
                if hasattr(self, 'smiles_preprocessor'):
                    cache_stats = self.smiles_preprocessor.cache_manager.get_stats()
                    f.write("缓存性能统计:\n")
                    f.write(f"  总请求数: {cache_stats['total_requests']}\n")
                    f.write(f"  缓存命中率: {cache_stats['hit_rate_percent']}%\n")
                    f.write(f"  内存缓存大小: {cache_stats['memory_cache_size']}\n")
                    f.write(f"  文件缓存大小: {cache_stats['file_cache_size']}\n")
                    f.write(f"  过期条目清理: {cache_stats['expired_entries']}\n\n")

                # 错误统计
                f.write("错误统计:\n")
                total_errors = 0
                for step, errors in all_errors.items():
                    error_count = len(errors)
                    total_errors += error_count
                    f.write(f"  {step}: {error_count} 个错误\n")
                f.write(f"  总错误数: {total_errors}\n\n")

                # 性能统计
                if hasattr(self.monitor, 'stats'):
                    f.write("性能统计:\n")
                    f.write(f"  总执行时间: {self.monitor.stats.total_duration:.2f} 秒\n")
                    f.write(f"  内存使用峰值: {max(self.monitor.stats.memory_usage.values()):.1f} MB\n")

                    # 各步骤时间
                    f.write("\n各步骤执行时间:\n")
                    for step, duration in self.monitor.stats.step_times.items():
                        f.write(f"  {step}: {duration:.2f} 秒\n")

            self.logger.info(f"处理报告已保存到: {report_path}")

        except Exception as e:
            self.logger.error(f"生成处理报告失败: {e}")

    def _generate_conversion_stats_report(self, success_df, error_records, warning_records, stats_file_path: str):
        """生成JSONL转换的详细统计报告"""
        try:
            from datetime import datetime
            
            # 计算基本统计
            # 注意：total_lines应该是原始输入文件的实际行数
            # 这里我们需要从转换器获取实际处理的行数
            if hasattr(self.jsonl_converter, 'total_processed_lines'):
                total_lines = self.jsonl_converter.total_processed_lines
            else:
                # 回退方案：成功行数 + 错误记录数（这可能不准确）
                total_lines = len(success_df) + len(error_records)
            
            success_count = len(success_df)
            error_count = len(error_records)
            warning_count = len(warning_records)
            success_rate = (success_count / total_lines * 100) if total_lines > 0 else 0

            # 统计错误类型
            error_stats = {}
            for error_record in error_records:
                errors = error_record.get('errors', [])
                for error in errors:
                    error_type = error.split(':')[0] if ':' in error else error
                    error_stats[error_type] = error_stats.get(error_type, 0) + 1

            # 统计警告类型
            warning_stats = {}
            for warning_record in warning_records:
                warnings = warning_record.get('warnings', [])
                for warning in warnings:
                    warning_type = warning.split(':')[0] if ':' in warning else warning
                    warning_stats[warning_type] = warning_stats.get(warning_type, 0) + 1

            # 生成统计报告
            stats_report = []
            stats_report.append(f"\n✅ 转换完成!")
            stats_report.append(f"📊 统计信息:")
            stats_report.append(f"  - 总处理行数: {total_lines:,}")
            stats_report.append(f"  - 成功转换: {success_count:,}")
            stats_report.append(f"  - 错误行数: {error_count:,}")
            stats_report.append(f"  - 警告行数: {warning_count:,}")
            stats_report.append(f"  - 成功率: {success_rate:.2f}%")

            # 错误统计
            if error_stats:
                stats_report.append(f"\n❌ 错误类型统计:")
                for error_type, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {error_type}: {count:,} 次")
            
            # 详细错误信息（显示前10个错误记录）
            if error_records:
                stats_report.append(f"\n📋 错误详情 (前10条):")
                for i, error_record in enumerate(error_records[:10]):
                    line_num = error_record.get('line_number', '未知')
                    errors = error_record.get('errors', [])
                    if isinstance(errors, list):
                        error_msg = '; '.join(errors[:2])  # 只显示前2个错误
                    else:
                        error_msg = str(errors)[:100]  # 限制长度
                    stats_report.append(f"  {i+1}. 第{line_num}行: {error_msg}")
                
                if len(error_records) > 10:
                    stats_report.append(f"  ... 还有 {len(error_records) - 10} 条错误记录")

            # 警告统计
            if warning_stats:
                stats_report.append(f"\n⚠️ 警告类型统计:")
                for warning_type, count in sorted(warning_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {warning_type}: {count:,} 次")

            # 获取转换器的统计信息（如果有的话）
            if hasattr(self.jsonl_converter, 'unknown_units') and self.jsonl_converter.unknown_units:
                stats_report.append(f"\n🔍 发现的未知单位:")
                for unit in sorted(self.jsonl_converter.unknown_units):
                    stats_report.append(f"  - {unit}")
                stats_report.append(f"\n💡 提示: 发现 {len(self.jsonl_converter.unknown_units)} 种未知单位，这些单位保持原样未进行转换。")

            if hasattr(self.jsonl_converter, 'parsed_specifications_success') and self.jsonl_converter.parsed_specifications_success:
                stats_report.append(f"\n✅ 成功解析的specification ({len(self.jsonl_converter.parsed_specifications_success)} 个):")
                for spec in sorted(self.jsonl_converter.parsed_specifications_success):
                    stats_report.append(f"  - {spec}")

            if hasattr(self.jsonl_converter, 'parsed_specifications_failed') and self.jsonl_converter.parsed_specifications_failed:
                total_failed_count = sum(self.jsonl_converter.parsed_specifications_failed_count.values())
                stats_report.append(f"\n❌ 解析失败的specification统计:")
                stats_report.append(f"  - 解析失败的specification种类数: {len(self.jsonl_converter.parsed_specifications_failed)} 个")
                stats_report.append(f"  - 解析失败的总次数: {total_failed_count:,} 次")
                stats_report.append(f"  - 平均每个specification失败次数: {total_failed_count/len(self.jsonl_converter.parsed_specifications_failed):.1f} 次")
                stats_report.append(f"\n❌ 解析失败的specification详情 ({len(self.jsonl_converter.parsed_specifications_failed)} 个):")
                sorted_failed = sorted(self.jsonl_converter.parsed_specifications_failed_count.items(), key=lambda x: x[1], reverse=True)
                for spec, count in sorted_failed:
                    if len(spec) > 80:
                        stats_report.append(f"  - {spec[:80]}... (失败 {count:,} 次)")
                    else:
                        stats_report.append(f"  - {spec} (失败 {count:,} 次)")

            # 输出到控制台
            for line in stats_report:
                print(line)

            # 写入统计信息到日志文件
            with open(stats_file_path, 'w', encoding='utf-8') as statsfile:
                statsfile.write(f"转换统计报告 - 生成时间: {datetime.now()}\n")
                statsfile.write("=" * 80 + "\n")
                for line in stats_report:
                    statsfile.write(line + "\n")

            self.logger.info(f"📄 统计报告已保存到: {stats_file_path}")

        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")

    def _save_step_errors(self, errors: List, file_path: str, error_type: str):
        """保存步骤错误到文件"""
        if not errors:
            return

        try:
            import json
            from pathlib import Path
            from datetime import datetime

            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            if error_type in ["duplicates"]:
                # 对于去重日志，保存为文本格式
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"去重日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        f.write(f"重复记录: {error}\n")
            elif file_path.endswith(".csv"):
                # 对于CSV格式的错误文件
                if errors and isinstance(errors[0], dict):
                    import pandas as pd
                    pd.DataFrame(errors).to_csv(file_path, index=False)
                else:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("error\n")
                        for error in errors:
                            f.write(f"{error}\n")
            else:
                # 对于日志格式的错误文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"警告日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        if isinstance(error, dict):
                            f.write(f"行号: {error.get('line_number', 'N/A')}\n")
                            f.write(f"错误: {error.get('errors', error.get('warnings', 'N/A'))}\n")
                            f.write("-" * 40 + "\n")
                        else:
                            f.write(f"{error}\n")

            self.logger.info(f"错误日志已保存到: {file_path}")

        except Exception as e:
            self.logger.error(f"保存{error_type}失败: {e}")

    def print_final_summary(self, paths: Dict[str, str]):
        """打印最终摘要"""
        from pathlib import Path

        summary_lines = []
        summary_lines.append("\n📋 处理完成摘要:")
        summary_lines.append(f"  📁 输出目录: {self.config.output_dir}")
        summary_lines.append(f"  📁 临时目录: {self.config.temp_dir}")
        summary_lines.append(f"  📁 日志目录: {self.config.log_dir}")
        summary_lines.append(f"  📄 最终数据文件: {paths['final_csv']}")
        summary_lines.append(f"  📝 重复记录日志: {paths['duplicates_log']}")

        # 统计文件大小
        final_csv_path = Path(paths["final_csv"])
        if final_csv_path.exists():
            size_mb = final_csv_path.stat().st_size / 1024 / 1024
            summary_lines.append(f"  📊 最终文件大小: {size_mb:.1f} MB")

        # 日志文件信息
        summary_lines.append("\n📝 日志文件:")
        log_files = [
            (paths["pipeline_log"], "主要运行日志"),
            (paths["full_execution_log"], "详细执行日志"),
        ]
        
        for log_file, description in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                size_kb = log_path.stat().st_size / 1024
                summary_lines.append(f"  📄 {description}: {log_file} ({size_kb:.1f} KB)")

        # 统计错误文件
        error_files = [
            (paths["step1_errors"], "步骤1转换错误"),
            (paths["step1_warnings"], "步骤1转换警告"),
            (paths["step2_errors"], "步骤2 Brain API错误"),
            (paths["step3_errors"], "步骤3 ClickHouse错误"),
        ]

        has_errors = False
        for error_file, description in error_files:
            error_path = Path(error_file)
            if error_path.exists() and error_path.stat().st_size > 0:
                if not has_errors:
                    summary_lines.append("\n❌ 错误文件:")
                    has_errors = True
                summary_lines.append(f"  📄 {description}: {error_file}")

        # 统计临时文件
        temp_files = [
            (paths["temp_step1_csv"], "步骤1中间结果"),
            (paths["temp_step2_csv"], "步骤2中间结果"),
            (paths["temp_step3_csv"], "步骤3中间结果"),
        ]

        summary_lines.append("\n📂 临时文件:")
        for temp_file, description in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                size_mb = temp_path.stat().st_size / 1024 / 1024
                summary_lines.append(f"  📄 {description}: {temp_file} ({size_mb:.1f} MB)")
        
        # 输出到控制台和日志
        for line in summary_lines:
            print(line)
            self.logger.info(line.strip())

    def cleanup_temp_files(self):
        """清理临时文件"""
        from pathlib import Path

        paths = self.get_file_paths()
        temp_files = [
            paths["temp_step1_csv"],
            paths["temp_step2_csv"],
            paths["temp_step3_csv"],
        ]

        for temp_file in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                temp_path.unlink()
                print(f"🗑️ 已删除临时文件: {temp_file}")
                self.logger.info(f"已删除临时文件: {temp_file}")

    async def save_results(
        self, df: pd.DataFrame, errors: Dict[str, List], output_dir: str
    ):
        """保存处理结果

        Args:
            df: 最终处理的DataFrame
            errors: 各步骤的错误记录
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存最终结果
        output_file = os.path.join(output_dir, "final_processed_data.csv")
        df.to_csv(output_file, index=False)
        self.logger.info(f"最终结果已保存到: {output_file}")

        # 保存各步骤的错误记录
        for step, error_list in errors.items():
            if error_list:
                error_file = os.path.join(output_dir, f"{step}_errors.json")
                import json

                with open(error_file, "w", encoding="utf-8") as f:
                    json.dump(error_list, f, ensure_ascii=False, indent=2)
                self.logger.info(f"{step}错误记录已保存到: {error_file}")


async def main():
    """主函数"""
    import argparse
    from pathlib import Path

    parser = argparse.ArgumentParser(
        description="模块化数据处理流水线",
        formatter_class=argparse.RawTextHelpFormatter,
    )

    parser.add_argument("input_file", help="输入JSONL文件路径")

    parser.add_argument(
        "--output-dir", default="./output", help="输出目录 (默认: ./output)"
    )

    parser.add_argument(
        "--temp-dir", default="./temp", help="临时文件目录 (默认: ./temp)"
    )

    parser.add_argument("--config-file", help="配置文件路径 (可选)")

    parser.add_argument(
        "--brain-api-batch-size",
        type=int,
        default=10000,
        help="Brain API批处理大小 (默认: 10000)",
    )

    parser.add_argument(
        "--clickhouse-host",
        default="localhost",
        help="ClickHouse主机地址 (默认: localhost)",
    )

    parser.add_argument(
        "--cleanup-temp", action="store_true", help="完成后清理临时文件"
    )

    parser.add_argument(
        "--step-timeout", type=int, default=3600, help="单步骤超时时间(秒) (默认: 3600)"
    )

    parser.add_argument(
        "--optimized", action="store_true",
        help="使用优化的两遍扫描流水线（阶段三批处理优化）"
    )

    parser.add_argument(
        "--clickhouse-batch-size",
        type=int,
        default=5000,
        help="ClickHouse批处理大小 (默认: 5000)",
    )

    args = parser.parse_args()

    # 先创建基础配置
    config = PipelineConfig(input_file=Path(args.input_file))

    # 如果提供了配置文件，先加载配置文件
    if args.config_file:
        config.load_from_file(args.config_file)

    # 然后用命令行参数覆盖配置（只覆盖非默认值）
    if args.output_dir != "./output":
        config.output_dir = Path(args.output_dir)
    if args.temp_dir != "./temp":
        config.temp_dir = Path(args.temp_dir)
    if args.brain_api_batch_size != 10000:
        config.brain_api_batch_size = args.brain_api_batch_size
    if args.clickhouse_host != "localhost":
        config.clickhouse_host = args.clickhouse_host
    if args.cleanup_temp:
        config.cleanup_temp = args.cleanup_temp
    if args.step_timeout != 3600:
        config.step_timeout = args.step_timeout

    # 验证配置
    print("🔍 验证配置...")
    if not config.validate():
        print("❌ 配置验证失败，流水线终止")
        return 1
    print("✅ 配置验证通过")

    # 创建并运行流水线
    print("🚀 初始化模块化数据处理流水线...")
    pipeline = ModularPipeline(config)
    
    print(f"📂 日志文件位置:")
    print(f"  主要日志: {config.log_dir / 'modular_pipeline.log'}")
    print(f"  详细日志: {config.log_dir / 'full_execution.log'}")
    print("\n🔄 开始执行流水线...")

    try:
        final_df, errors = await pipeline.run_pipeline(args.input_file)

        # 清理临时文件（如果配置允许）
        if config.cleanup_temp:
            print("🧹 清理临时文件...")
            pipeline.cleanup_temp_files()

        print(f"\n🎉 流水线执行完成! 最终处理了 {len(final_df)} 行数据")
        print(f"📁 结果已保存到: {config.output_dir}")
        print(f"📝 详细日志已保存到: {config.log_dir}")

    except Exception as e:
        print(f"💥 流水线执行失败: {e}")
        # 确保错误也记录到日志中
        if 'pipeline' in locals():
            pipeline.logger.error(f"流水线执行失败: {e}")
            pipeline.logger.exception("详细错误信息:")
        return 1

    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
