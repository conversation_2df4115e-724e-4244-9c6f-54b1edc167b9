import { MaterialItem } from "../../..";
import { Basic, getInfoFromCasOrNameBatch } from "../../clickhouse";
import { getCasFromSynonyms, getNameEnFromSynonyms } from "./casAndName";

export const addSmilesBySynonym = async <
  T extends Partial<
    Pick<
      MaterialItem,
      "inchified_smiles" | "canonical_smiles" | "cas_no" | "name_en"
    >
  >
>(
  rows: T[]
): Promise<(T & Pick<MaterialItem, "cas_no" | "name_en">)[]> => {
  const querys = new Set<string>();
  rows
    .filter((r) => !r.canonical_smiles)
    .forEach((r) => {
      if (r.cas_no) querys.add(r.cas_no);
      if (r.name_en) querys.add(r.name_en);
    });

  if (querys.size) {
    const infos = await getInfoFromCasOrNameBatch(querys);

    const misses: Set<{ cas: string; name: string }> = new Set();
    const multiple: Set<{ cas: string; name: string }> = new Set();

    const result = rows.map((r) => {
      const nameResult = infos[r.name_en] || [];
      const casResult = infos[r.cas_no] || [];
      let result: Basic | undefined = undefined;
      if (!nameResult.length && !casResult.length) {
        misses.add({ cas: r.cas_no, name: r.name_en });
      } else if (nameResult.length === 1) {
        result = nameResult[0];
      } else if (casResult.length === 1) {
        result = casResult[0];
      } else if (nameResult.length > 1 && casResult.length > 1) {
        multiple.add({ cas: r.cas_no, name: r.name_en });
        result = casResult[0];
      } else if (casResult.length > 1) {
        result = casResult[0];
      } else {
        result = nameResult[0];
      }

      if (!result) return r;

      return {
        ...r,
        inchified_smiles: r.inchified_smiles || result?.inchified_smiles,
        canonical_smiles: r.canonical_smiles || result?.canonical_smiles,
        cas_no:
          r.cas_no || getCasFromSynonyms(nameResult.map((r) => r.synonym)),
        name_en:
          r.name_en || getNameEnFromSynonyms(casResult.map((r) => r.synonym)),
      };
    });

    return result;
  }
  return rows as (T &
    Pick<MaterialItem, "inchified_smiles" | "cas_no" | "name_en">)[];
};
