import { MaterialItem } from "../../..";
import { HeaderArray } from "@fast-csv/parse";
import { convert } from "./cnCommon";
import { validateWithBool } from "../../utils";
import { addUnitPrice } from "../fillInfo/unitPrice";
import { providerIdMap } from "./idMap";

export const headers = [
  "brand", // 乐研
  "stockNumber", // 1014478-25g
  "CAS", // 10004-44-1
  "nameEn", // 5-Methylisoxazol-3(2H)-one
  "nameZh", // 5-甲基异噁唑-3-醇
  "purity", // 98%
  "stock", // in stock
  "specification", // 25g
  "price", // 55
] as const;

export type LeyanItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: LeyanItem): MaterialItem => {
  const mid = {
    material_lib: providerIdMap.leyan,

    ...convert(row),

    material_id: `leyan-${row.stockNumber}`,

    in_stock: row.stock === "in stock",
    max_delivery_days: undefined,
    min_delivery_days: undefined,
    purity: row.purity,

    source: "leyan",
    source_link: `https://www.leyan.com/${row.CAS}.html`,
    extension: undefined,
  };

  return addUnitPrice(mid);
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
