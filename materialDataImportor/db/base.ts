import { Pool } from "pg";
import { config } from "dotenv";

config({ path: ".env" });

const pool = new Pool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  port: process.env.DB_PORT,
});

export const singleQuery = async (query: string) => {
  const client = await pool.connect();
  try {
    const res = await client.query(query);
    if (res.command === "SELECT") return res.rows;
    return res;
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    client.release();
  }
};

export const transaction = async (queries: string[]) => {
  const client = await pool.connect();
  try {
    await client.query("BEGIN");
    for (const query of queries) {
      await client.query(query);
    }
    await client.query("COMMIT");
  } catch (e) {
    await client.query("ROLLBACK");
    throw e;
  } finally {
    client.release();
  }
};
