# Brain Server API Client

一个用于调用 brain-server API 的 Python 工具，支持 SMILES 字符串的批量处理、去重和并行调用。

## 功能特性

- **批量处理**: 自动将大量 SMILES 分批处理，单次 API 调用最多处理 10,000 条 SMILES
- **去重优化**: 自动对输入进行去重，减少不必要的 API 调用
- **并行处理**: canonicalize 和 inchi 两个 API 并行调用，提高处理效率
- **顺序保持**: 输出结果保持与输入相同的顺序
- **异步支持**: 支持异步和同步两种调用方式
- **错误处理**: 完善的错误处理和重试机制

## 安装依赖

```bash
pip install -r requirements_brain_client.txt
```

或者手动安装：

```bash
pip install aiohttp>=3.8.0
```

## 配置

设置环境变量：

```bash
export BRAIN_SERVER_URL="https://your-brain-server.com"
export BRAIN_SERVER_TOKEN="your-bearer-token"
```

## 使用方法

### 基本用法

```python
from brain_server_client import BrainServerClient

# 创建客户端实例
client = BrainServerClient(base_url="http://localhost:8080")

# 处理 SMILES 列表
smiles_list = ["CCO", "CC(=O)O", "c1ccccc1"]
results = client.process_smiles_sync(smiles_list)

# 输出结果
for i, result in enumerate(results):
    print(f"Input: {smiles_list[i]}")
    canonical = result['canonical_smiles']
    inchified = result['inchified_smiles']
    
    if canonical is None or inchified is None:
        print(f"  Invalid SMILES detected")
    else:
        print(f"  Canonical: {canonical}")
        print(f"  Inchified: {inchified}")
```

**注意**: 确保 `brain_server_client.py` 文件在同一目录下，或者根据实际路径调整导入语句。

### 高级用法（异步）

```python
import asyncio
from brain_server_client import BrainServerClient

async def main():
    client = BrainServerClient(base_url="http://localhost:8080")
    
    # 处理大量 SMILES
    large_smiles_list = ["CCO"] * 1000  # 1000个相同的SMILES用于测试
    results = await client.process_smiles(large_smiles_list)
    
    print(f"Processed {len(results)} SMILES")
    
    # 关闭客户端
    await client.close()

# 运行异步函数
asyncio.run(main())
```

### 自定义批次大小

```python
# 创建客户端时指定批次大小
client = BrainServerClient(
    base_url="http://localhost:8080",
    bearer_token="your_token_here",
    batch_size=100  # 每批处理100个SMILES
)

results = client.process_smiles_sync(smiles_list)
```

## API 接口

### `process_smiles(smiles_input, base_url, bearer_token, batch_size=10000)`

便捷函数，用于快速处理 SMILES。

**参数：**
- `smiles_input`: `str | List[str]` - 单个 SMILES 字符串或 SMILES 列表
- `base_url`: `str` - brain-server API 的基础 URL
- `bearer_token`: `str` - 认证令牌
- `batch_size`: `int` - 批次大小（默认 10000）

**返回：**
- `List[Dict[str, str]]` - 包含 `canonical_smiles` 和 `inchified_smiles` 的字典列表

### `BrainServerClient`

主要的客户端类。

**方法：**
- `__init__(base_url, bearer_token, batch_size=10000)` - 初始化客户端
- `async process_smiles(smiles_input)` - 异步处理 SMILES
- `process_smiles_sync(smiles_input)` - 同步处理 SMILES

## 输出格式

每个输入的 SMILES 都会返回一个包含以下字段的字典：

```python
{
    "canonical_smiles": "规范化的 SMILES 字符串" | None,
    "inchified_smiles": "InChI 化的 SMILES 字符串" | None
}
```

**注意**: 当 API 返回 "CC" 时，表示输入的 SMILES 非法，此时对应的字段值将被设置为 `None`。如果任一 API 返回无效结果，两个字段都会被设置为 `None`。

## 性能特性

1. **去重处理**: 自动识别重复的 SMILES，只处理唯一值，然后映射回原始顺序
2. **批量处理**: 大数据集自动分批，避免单次请求过大
3. **并行调用**: canonicalize 和 inchi 接口并行调用，减少总处理时间
4. **串行批次**: 同一接口的不同批次串行处理，避免服务器过载

## 示例

查看 `brain_client_example.py` 文件获取更多使用示例，包括：
- 基本用法
- 单个 SMILES 处理
- 异步处理
- 大数据集处理

运行示例：

```bash
python brain_client_example.py
```

## 错误处理

工具包含完善的错误处理：
- API 调用失败时抛出详细错误信息
- 网络超时和连接错误的处理
- 无效 SMILES 的处理

## 注意事项

1. 确保 brain-server API 服务可用且认证信息正确
2. 大数据集处理时注意 API 速率限制
3. 建议在生产环境中添加重试机制和日志记录
4. API 响应格式可能需要根据实际服务器实现进行调整

## 许可证

本项目遵循 MIT 许可证。