import { MaterialItem } from "../../..";
import { parseSpecification } from "../../utils";
import { AladingItem } from "./alading";
import { BideItem } from "./bide";
import { LeyanItem } from "./leyan";

export const convert = (
  row: LeyanItem | BideItem | AladingItem
): Pick<
  MaterialItem,
  | "canonical_smiles"
  | "inchified_smiles"
  | "quantity"
  | "unit"
  | "price"
  | "cas_no"
  | "name_en"
  | "name_zh"
  | "codes"
  | "pubchem_safety_link"
  | "unified_smiles"
> => {
  const { quantity, unit } = parseSpecification(row.specification);
  const price = Number.parseFloat(row.price);

  return {
    canonical_smiles: "",
    inchified_smiles: "",

    cas_no: row.CAS,
    name_en: row.nameEn,
    name_zh: row.nameZh,

    quantity,
    unit,
    price,

    codes: [],
    pubchem_safety_link: "",
    unified_smiles: "",
  };
};
