/**
 * 化学材料产品数据类型定义
 * 用于定义化学试剂和材料产品的标准数据结构
 */

import { Currency, SpecUnit, Vendor, VersionNum, StockStatus, Region } from './enums';

/**
 * 库存详情接口
 * 描述产品在不同地区的库存状态
 */
interface StockDetail {
  /** 库存地区/仓库位置，如："上海"、"天津"、"日本"等，可为空字符串 */
  region: Region | string;
  /** 库存状态描述，如："1"（具体数量）、"现货"、"5~7个工作日"、"从上海仓库发货"等 */
  stock: StockStatus | string;
}

/**
 * 化学材料产品接口
 * 包含化学试剂产品的完整信息
 */
interface ChemicalProduct {
  /** 货号，同一库存的唯一ID，如：'bg8riE4KQm8GgwiKFzyA43' */
  sku_id: string;
  /** CAS号（化学文摘社登记号），如："31224-43-8"，可空（占比不高于5%） */
  cas: string;
  /** 产品中文名，如："3-氟吡啶-2-甲醛"，可空（占比不高于10%） */
  name_zh: string;
  /** IUPAC标准化学名称，如："3-Fluoro-2-formylpyridine"，可空（占比不高于5%） */
  iupac_name: string[];
  /** 产品价格，两位小数，如：29.99 */
  price: number;
  /** 货币单位，如："RMB"，可空时默认为人民币 */
  currency: Currency;
  /** 原始规格描述，如："25ml" */
  specification: string;
  /** 规格对应的数量，如：25.00，可空 */
  spec_quantity: number;
  /** 规格对应的单位，如："ml"，可空 */
  spec_unit: SpecUnit;
  /** 供应商名称，如："Aladdin" */
  vendor: Vendor;
  /** 分子的InChI Key标识符，如："OZIMPUNGBUYCSP-UHFFFAOYSA-N"，可空（占比不高于5%） */
  inchikey: string[];
  /** 分子canonicalize后的SMILES，如："C1=CC(=C(N=C1)C=O)F"，可空（占比不高于5%） */
  cano_smiles: string[];
  /** 产品数据对应的网页URL */
  product_url: string;
  /** 是否在库，只要有一个渠道在库则为true，反之为false */
  in_stock: boolean;
  /** 原始库存状态列表，提供各地区的详细库存信息 */
  stock_detail: StockDetail[];
  /** 数据版本号，如："V1" */
  version_num: VersionNum;
  /** 删除状态标记，该条数据为删除状态时值为true；正常数据无本字段或为false */
  is_delete: boolean;
  /** 记录创建的时间戳（毫秒），如：1734924841251.6082 */
  create_time: number;
  /** 任意字段修改时记录的最后修改时间戳（毫秒），如：1734924841251.6082 */
  last_modfied_time: number;
}

export type { ChemicalProduct, StockDetail };