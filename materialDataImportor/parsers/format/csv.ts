import {
  testSize,
  getWriteStreamWithPromise,
  getReadStream,
} from "../../utils";
import { handleStream } from "../stream";
import { Provider } from "../../run";
import { MaterialItem } from "../../..";

export const parseToCsv = async (
  type: Provider,
  inPath: string,
  outDir: string,
  version: string,
  testMode: boolean = true
) => {
  const [outStream, pipe, outP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}.csv`
  );
  const [errStream, errPipe, errP] = getWriteStreamWithPromise(
    `${outDir}/${type}-${version}-err.csv`
  );

  const parseStream = getReadStream(inPath, {
    headers: true,
    maxRows: testMode ? testSize : undefined,
  });

  pipe();
  errPipe();
  await handleStream(
    async (r: MaterialItem) => r,
    parseStream,
    outStream,
    errStream
  );
  outStream.end();
  errStream.end();

  return Promise.all([outP, errP]);
};
