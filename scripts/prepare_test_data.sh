#!/bin/bash
# -*- coding: utf-8 -*-
"""
测试数据准备脚本

从原始数据中创建不同大小的测试数据集，用于验证流水线的正确性和性能

用法:
    bash prepare_test_data.sh [原始数据文件路径]
"""

set -e  # 遇到错误立即退出

# 默认配置
ORIGINAL_DATA="/Users/<USER>/Work/material-data/data/origin/product_client_250609.jsonl.tar.gz"
TEST_DATA_DIR="/Users/<USER>/Work/material-data/test_data"

# 如果提供了参数，使用参数作为原始数据路径
if [ $# -gt 0 ]; then
    ORIGINAL_DATA="$1"
fi

echo "🚀 开始准备测试数据..."
echo "📁 原始数据文件: $ORIGINAL_DATA"
echo "📁 测试数据目录: $TEST_DATA_DIR"

# 创建测试数据目录
mkdir -p "$TEST_DATA_DIR"

# 检查原始数据文件是否存在
if [ ! -f "$ORIGINAL_DATA" ]; then
    echo "❌ 错误: 原始数据文件不存在: $ORIGINAL_DATA"
    echo "请检查文件路径或提供正确的文件路径作为参数"
    echo "用法: bash prepare_test_data.sh [原始数据文件路径]"
    exit 1
fi

# 解压原始数据（如果是压缩文件）
TEMP_JSONL="$TEST_DATA_DIR/temp_original.jsonl"
echo "📦 解压原始数据..."

if [[ "$ORIGINAL_DATA" == *.tar.gz ]]; then
    tar -xzf "$ORIGINAL_DATA" -O > "$TEMP_JSONL"
elif [[ "$ORIGINAL_DATA" == *.gz ]]; then
    gunzip -c "$ORIGINAL_DATA" > "$TEMP_JSONL"
elif [[ "$ORIGINAL_DATA" == *.jsonl ]]; then
    cp "$ORIGINAL_DATA" "$TEMP_JSONL"
else
    echo "❌ 错误: 不支持的文件格式，支持的格式: .jsonl, .jsonl.gz, .jsonl.tar.gz"
    exit 1
fi

# 检查解压后的文件
if [ ! -f "$TEMP_JSONL" ]; then
    echo "❌ 错误: 解压失败"
    exit 1
fi

# 统计总行数
TOTAL_LINES=$(wc -l < "$TEMP_JSONL")
echo "📊 原始数据总行数: $TOTAL_LINES"

# 创建不同大小的测试数据集
echo "\n📝 创建测试数据集..."

# 1000条数据（快速测试）
echo "  - 创建1000条数据样本..."
head -1000 "$TEMP_JSONL" > "$TEST_DATA_DIR/sample_1k.jsonl"
echo "    ✅ 已创建: $TEST_DATA_DIR/sample_1k.jsonl"

# 5000条数据（中等测试）
if [ $TOTAL_LINES -ge 5000 ]; then
    echo "  - 创建5000条数据样本..."
    head -5000 "$TEMP_JSONL" > "$TEST_DATA_DIR/sample_5k.jsonl"
    echo "    ✅ 已创建: $TEST_DATA_DIR/sample_5k.jsonl"
else
    echo "    ⚠️ 跳过5000条样本（原始数据不足5000条）"
fi

# 10000条数据（标准验证）
if [ $TOTAL_LINES -ge 10000 ]; then
    echo "  - 创建10000条数据样本..."
    head -10000 "$TEMP_JSONL" > "$TEST_DATA_DIR/sample_10k.jsonl"
    echo "    ✅ 已创建: $TEST_DATA_DIR/sample_10k.jsonl"
else
    echo "    ⚠️ 跳过10000条样本（原始数据不足10000条）"
fi

# 50000条数据（性能测试）
if [ $TOTAL_LINES -ge 50000 ]; then
    echo "  - 创建50000条数据样本..."
    head -50000 "$TEMP_JSONL" > "$TEST_DATA_DIR/sample_50k.jsonl"
    echo "    ✅ 已创建: $TEST_DATA_DIR/sample_50k.jsonl"
else
    echo "    ⚠️ 跳过50000条样本（原始数据不足50000条）"
fi

# 100000条数据（大规模测试）
if [ $TOTAL_LINES -ge 100000 ]; then
    echo "  - 创建100000条数据样本..."
    head -100000 "$TEMP_JSONL" > "$TEST_DATA_DIR/sample_100k.jsonl"
    echo "    ✅ 已创建: $TEST_DATA_DIR/sample_100k.jsonl"
else
    echo "    ⚠️ 跳过100000条样本（原始数据不足100000条）"
fi

# 创建随机样本（用于多样性测试）
if [ $TOTAL_LINES -ge 20000 ]; then
    echo "  - 创建10000条随机样本..."
    shuf "$TEMP_JSONL" | head -10000 > "$TEST_DATA_DIR/sample_10k_random.jsonl"
    echo "    ✅ 已创建: $TEST_DATA_DIR/sample_10k_random.jsonl"
else
    echo "    ⚠️ 跳过随机样本（原始数据不足20000条）"
fi

# 清理临时文件
rm -f "$TEMP_JSONL"

# 创建测试数据信息文件
INFO_FILE="$TEST_DATA_DIR/test_data_info.txt"
echo "📋 创建测试数据信息文件..."

cat > "$INFO_FILE" << EOF
测试数据信息
=============

生成时间: $(date)
原始数据文件: $ORIGINAL_DATA
原始数据总行数: $TOTAL_LINES

测试数据集:
EOF

# 统计每个测试文件的信息
for file in "$TEST_DATA_DIR"/sample_*.jsonl; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        lines=$(wc -l < "$file")
        size=$(du -h "$file" | cut -f1)
        echo "- $filename: $lines 行, $size" >> "$INFO_FILE"
    fi
done

cat >> "$INFO_FILE" << EOF

使用说明:
- sample_1k.jsonl: 快速功能测试
- sample_5k.jsonl: 中等规模测试
- sample_10k.jsonl: 标准验证测试（推荐用于第一阶段验证）
- sample_50k.jsonl: 性能测试
- sample_100k.jsonl: 大规模测试
- sample_10k_random.jsonl: 随机样本测试（用于验证数据多样性）

建议测试顺序:
1. 使用 sample_1k.jsonl 进行快速功能验证
2. 使用 sample_10k.jsonl 进行完整流程验证
3. 使用 sample_50k.jsonl 进行性能测试
EOF

echo "    ✅ 已创建: $INFO_FILE"

# 显示最终统计
echo "\n📊 测试数据准备完成!"
echo "📁 测试数据目录: $TEST_DATA_DIR"
echo "📋 数据集信息:"

for file in "$TEST_DATA_DIR"/sample_*.jsonl; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        lines=$(wc -l < "$file")
        size=$(du -h "$file" | cut -f1)
        echo "  - $filename: $lines 行, $size"
    fi
done

echo "\n💡 使用建议:"
echo "  1. 快速测试: python3 unified_pipeline.py $TEST_DATA_DIR/sample_1k.jsonl"
echo "  2. 标准验证: python3 unified_pipeline.py $TEST_DATA_DIR/sample_10k.jsonl"
echo "  3. 性能测试: python3 unified_pipeline.py $TEST_DATA_DIR/sample_50k.jsonl"

echo "\n🎉 测试数据准备完成！"