#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件更新脚本 - 使用Brain Server API

基于brain_server_client工具更新CSV文件中的canonical_smiles和inchified_smiles列
对于无效的SMILES，将行移动到error.csv文件中并添加错误原因

用法:
    python3 update_csv_with_brain_api.py input.csv output.csv error.csv
"""

import sys
import os
import csv
import asyncio
import argparse
import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 添加brain_server_client模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'brain_server_client'))

try:
    from brain_server_client import BrainServerClient, SmilesResult
except ImportError:
    print("错误: 无法导入brain_server_client模块")
    print("请确保brain_server_client目录在正确位置")
    sys.exit(1)

class CSVUpdater:
    def __init__(self, batch_size: int = 10000):
        self.client = BrainServerClient()
        self.batch_size = batch_size
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
    async def process_batch(self, batch: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        处理一批数据
        返回: (成功更新的行, 错误行)
        """
        if not batch:
            return [], []

        rows_to_process = []
        error_rows = []
        success_rows = []

        # 分类处理行
        for row in batch:
            canonical_smiles = row.get('canonical_smiles', '').strip()
            if canonical_smiles == 'CC':
                row['canonical_smiles'] = 'CC'
                row['inchified_smiles'] = 'CC'
                success_rows.append(row)
            elif canonical_smiles:
                rows_to_process.append(row)
            else:
                row['error_reason'] = 'canonical_smiles字段为空'
                error_rows.append(row)

        if not rows_to_process:
            return success_rows, error_rows

        # 提取需要API处理的SMILES，并去重
        unique_smiles = list(set(r['canonical_smiles'] for r in rows_to_process))

        try:
            # 调用API处理唯一的SMILES
            results_list = await self.client.process_smiles(unique_smiles)
            
            # 将结果映射回SMILES字符串
            smiles_to_result = {smiles: result for smiles, result in zip(unique_smiles, results_list)}

            # 处理所有待处理的行
            for row in rows_to_process:
                smiles = row['canonical_smiles']
                result = smiles_to_result.get(smiles)

                if not result or not result.canonical_smiles or not result.inchified_smiles:
                    # API返回None或空字符串表示无效SMILES
                    row['error_reason'] = f'API返回无效SMILES: canonical={result.canonical_smiles if result else None}, inchified={result.inchified_smiles if result else None}'
                    error_rows.append(row)
                else:
                    # 更新SMILES字段
                    row['canonical_smiles'] = result.canonical_smiles
                    row['inchified_smiles'] = result.inchified_smiles
                    success_rows.append(row)

        except Exception as e:
            # API调用失败，所有待处理行都标记为错误
            for row in rows_to_process:
                row['error_reason'] = f'API调用失败: {str(e)}'
                error_rows.append(row)

        return success_rows, error_rows
    
    async def update_csv(self, input_file: str, output_file: str, error_file: str):
        """
        更新CSV文件
        """
        print(f"开始处理文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"错误文件: {error_file}")
        print(f"批处理大小: {self.batch_size}")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件不存在: {input_file}")
            return
            
        # 读取CSV头部
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames
            
        if not fieldnames:
            print("错误: 无法读取CSV头部")
            return
            
        # 检查必要字段
        if 'canonical_smiles' not in fieldnames:
            print("错误: CSV文件中缺少canonical_smiles字段")
            return
            
        # 为错误文件添加error_reason字段
        error_fieldnames = fieldnames + ['error_reason']
        
        # 初始化输出文件
        with open(output_file, 'w', newline='', encoding='utf-8') as success_f, \
             open(error_file, 'w', newline='', encoding='utf-8') as error_f:
            
            success_writer = csv.DictWriter(success_f, fieldnames=fieldnames)
            error_writer = csv.DictWriter(error_f, fieldnames=error_fieldnames)
            
            success_writer.writeheader()
            error_writer.writeheader()
            
            # 流式处理CSV文件
            with open(input_file, 'r', encoding='utf-8') as input_f:
                reader = csv.DictReader(input_f)
                batch = []
                
                for row in reader:
                    batch.append(row)
                    
                    if len(batch) >= self.batch_size:
                        # 处理批次
                        success_rows, error_rows = await self.process_batch(batch)
                        
                        # 写入结果
                        for success_row in success_rows:
                            success_writer.writerow(success_row)
                            self.success_count += 1
                            
                        for error_row in error_rows:
                            error_writer.writerow(error_row)
                            self.error_count += 1
                            
                        self.processed_count += len(batch)
                        print(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
                        
                        batch = []
                        
                # 处理最后一批
                if batch:
                    success_rows, error_rows = await self.process_batch(batch)
                    
                    for success_row in success_rows:
                        success_writer.writerow(success_row)
                        self.success_count += 1
                        
                    for error_row in error_rows:
                        error_writer.writerow(error_row)
                        self.error_count += 1
                        
                    self.processed_count += len(batch)
                    
        print(f"\n处理完成!")
        print(f"总处理行数: {self.processed_count}")
        print(f"成功更新: {self.success_count}")
        print(f"错误行数: {self.error_count}")
        print(f"成功率: {self.success_count/self.processed_count*100:.2f}%" if self.processed_count > 0 else "成功率: 0%")

        # 验证文件行数
        self._verify_row_counts(input_file, output_file, error_file)

    def _verify_row_counts(self, input_file: str, output_file: str, error_file: str):
        try:
            # 获取行数，-1是为了去掉header
            with open(input_file, 'r', encoding='utf-8') as f:
                input_count = sum(1 for _ in f) - 1
            
            with open(output_file, 'r', encoding='utf-8') as f:
                output_count = sum(1 for _ in f) - 1

            with open(error_file, 'r', encoding='utf-8') as f:
                error_count = sum(1 for _ in f) - 1

            print("\n--- 文件行数验证 ---")
            print(f"原始文件行数: {input_count}")
            print(f"成功文件行数: {output_count}")
            print(f"错误文件行数: {error_count}")

            if input_count == output_count + error_count:
                print("✅ 验证通过: 总行数匹配")
            else:
                print(f"❌ 验证失败: 总行数不匹配! 差值: {input_count - (output_count + error_count)}")

        except FileNotFoundError as e:
            print(f"行数验证失败: 文件未找到 {e.filename}")
        except Exception as e:
            print(f"行数验证时发生未知错误: {e}")
    
    async def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """处理DataFrame数据
        
        Args:
            df: 输入的DataFrame
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (成功数据DataFrame, 错误数据DataFrame)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"开始处理DataFrame，共 {len(df)} 行")
        
        success_data = []
        error_data = []
        
        # 重置计数器
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 分批处理
        for i in range(0, len(df), self.batch_size):
            batch_df = df.iloc[i:i + self.batch_size]
            batch = batch_df.to_dict('records')
            
            # 处理批次
            success_rows, error_rows = await self.process_batch(batch)
            
            # 收集结果
            success_data.extend(success_rows)
            error_data.extend(error_rows)
            
            self.processed_count += len(batch)
            self.success_count += len(success_rows)
            self.error_count += len(error_rows)
            
            logger.info(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
        
        # 创建结果DataFrame
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_df = pd.DataFrame(error_data) if error_data else pd.DataFrame()
        
        logger.info(f"处理完成: 总处理行数: {self.processed_count}, 成功: {self.success_count}, 错误: {self.error_count}")
        
        return success_df, error_df
    
    async def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """更新DataFrame中的SMILES数据
        
        Args:
            df: 输入的DataFrame，必须包含canonical_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        logger = logging.getLogger(__name__)
        
        # 检查必要字段
        if 'canonical_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少canonical_smiles字段")
        
        logger.info(f"开始更新DataFrame中的SMILES数据，共 {len(df)} 行")
        
        success_df, error_df = await self.process_dataframe(df)
        
        # 转换错误数据为记录列表
        error_records = error_df.to_dict('records') if not error_df.empty else []
        
        return success_df, error_records

async def main():
    parser = argparse.ArgumentParser(
        description='使用Brain Server API更新CSV文件中的SMILES数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""示例:
  python3 update_csv_with_brain_api.py data.csv
  python3 update_csv_with_brain_api.py data.csv --batch-size 5000
  python3 update_csv_with_brain_api.py data.csv -o updated.csv -e errors.csv
  python3 update_csv_with_brain_api.py data.csv updated.csv errors.csv --batch-size 20000"""
    )
    
    parser.add_argument('input_file', help='输入的CSV文件')
    parser.add_argument('output_file', nargs='?', help='成功更新的输出CSV文件 (可选，默认为输入文件名_updated.csv)')
    parser.add_argument('error_file', nargs='?', help='包含错误行的CSV文件 (可选，默认为输入文件名_errors.csv)')
    parser.add_argument('-o', '--output', dest='output_file_flag', help='指定输出文件 (与位置参数二选一)')
    parser.add_argument('-e', '--error', dest='error_file_flag', help='指定错误文件 (与位置参数二选一)')
    parser.add_argument('-b', '--batch-size', type=int, default=10000, help='批处理大小 (默认: 10000)')
    parser.add_argument('--version', action='version', version='CSV Updater 1.0')
    
    args = parser.parse_args()
    
    input_file = args.input_file
    
    # 生成默认的输出文件名
    input_dir = os.path.dirname(input_file)
    input_name = os.path.splitext(os.path.basename(input_file))[0]
    input_ext = os.path.splitext(input_file)[1]
    
    # 确定输出文件名 (优先级: 命令行flag > 位置参数 > 默认值)
    if args.output_file_flag:
        output_file = args.output_file_flag
    elif args.output_file:
        output_file = args.output_file
    else:
        output_file = os.path.join(input_dir, f"{input_name}_updated{input_ext}")
        
    # 确定错误文件名
    if args.error_file_flag:
        error_file = args.error_file_flag
    elif args.error_file:
        error_file = args.error_file
    else:
        error_file = os.path.join(input_dir, f"{input_name}_errors{input_ext}")
    
    # 验证批处理大小
    if args.batch_size <= 0:
        print("错误: 批处理大小必须大于0")
        sys.exit(1)
        
    if args.batch_size > 50000:
        print("警告: 批处理大小过大可能导致内存问题，建议不超过50000")
        response = input("是否继续? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            sys.exit(0)
    
    # 创建更新器并处理文件
    updater = CSVUpdater(batch_size=args.batch_size)
    
    try:
        await updater.update_csv(input_file, output_file, error_file)
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"\n处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())