#!/bin/bash

# nohup ./main.sh < logs_material_import_input > logs 2&>1 &

# Ask for input
echo "Enter provider type (emolecule, mcule, leyan, alading, bide):"
read provider
echo "Enter version (like 2014-01-01):"
read version
echo "Enter input file path:"
read in_file
echo "Enter output director path (default data/out):"
read out_dir
out_dir=${out_dir:-data/out}
echo "Enter updator user id (the user id who update the materials, default 1):"
read updator
updator=${updator:-1}

log_filename="logs_$(date +%Y-%m-%d-%H-%M-%S)"
echo "Converting data to csv formated file, and dump to DB. Logs in $log_filename"
yarn ts-node ./materialDataImportor/run.ts $provider $version $in_file $out_dir $updator >$log_filename
echo "Convert and dump ended, out file in $out_dir/$provider-$version.csv and validated failed file in $out_dir/$provider-$version-err.csv"
