#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deduplicate and update a CSV file.

This script processes a CSV file to deduplicate entries based on 'material_id' 
and updates a 'lowest_unit_price' field based on 'inchified_smiles' and 'unit_price'.

- Deduplication Logic:
  - Rows with the same 'material_id' are considered duplicates.
  - If duplicate 'material_id' rows have identical data, they are counted as simple duplicates.
  - If duplicate 'material_id' rows have different data in other fields, they are flagged as warnings, 
    and the conflicting rows are saved to a separate warnings file.
  - The main output contains only the first occurrence of each 'material_id'.

- Update Logic:
  - For each group of rows with the same 'inchified_smiles', the script identifies the row with the
    lowest 'unit_price' and sets its 'lowest_unit_price' field to True. All other rows in the group
    will have this field set to False.

Usage:
    python3 deduplicate_and_update_prices.py input.csv output.csv duplicates.log warnings.csv
"""

import pandas as pd
import argparse
import os
import logging
from typing import Tuple, List, Dict

def process_csv(input_file: str, output_file: str, log_file: str, warning_file: str):
    """
    Deduplicates and updates the given CSV file.

    Args:
        input_file: Path to the input CSV file.
        output_file: Path to write the processed data.
        log_file: Path to write the duplicate log.
        warning_file: Path to write conflicting (warning) rows.
    """
    if not os.path.exists(input_file):
        print(f"Error: Input file not found: {input_file}")
        return

    print(f"Reading data from {input_file}...")
    df = pd.read_csv(input_file)

    # --- Deduplication Step ---
    print("Deduplicating data based on 'material_id'...")
    
    # Find all duplicates based on material_id
    duplicates_mask = df.duplicated(subset=['material_id'], keep=False)
    duplicate_rows = df[duplicates_mask]

    # Separate fully identical duplicates from conflicting duplicates
    fully_identical_mask = df.duplicated(keep=False)
    conflicting_rows = duplicate_rows[~duplicate_rows.index.isin(df[fully_identical_mask].index)]

    # Get the final deduplicated dataframe (keeping the first instance)
    deduplicated_df = df.drop_duplicates(subset=['material_id'], keep='first').copy()

    # --- Logging Duplicates and Warnings ---
    with open(log_file, 'w') as log:
        log.write("Duplicate Log\n")
        log.write("==============\n")
        duplicate_counts = df.groupby('material_id').size().reset_index(name='counts')
        duplicate_counts = duplicate_counts[duplicate_counts['counts'] > 1]
        for _, row in duplicate_counts.iterrows():
            log.write(f"material_id '{row['material_id']}' was repeated {row['counts']} times.\n")

    if not conflicting_rows.empty:
        print(f"Found {len(conflicting_rows)} conflicting rows. Saving to {warning_file}")
        conflicting_rows.to_csv(warning_file, index=False)
    else:
        print("No conflicting rows found.")

    # --- Update lowest_unit_price Step ---
    print("Updating 'lowest_unit_price' field...")
    
    # Ensure 'unit_price' is numeric, coercing errors
    deduplicated_df['unit_price'] = pd.to_numeric(deduplicated_df['unit_price'], errors='coerce')
    
    # Find the index of the minimum unit_price for each inchified_smiles group
    idx = deduplicated_df.groupby(['inchified_smiles'])['unit_price'].idxmin()
    
    # Initialize the column with False
    deduplicated_df['lowest_unit_price'] = False
    
    # Set to True for the rows with the lowest unit price
    deduplicated_df.loc[idx, 'lowest_unit_price'] = True

    # --- Save Final Output ---
    print(f"Saving processed data to {output_file}...")
    deduplicated_df.to_csv(output_file, index=False)

    print("\nProcessing finished!")
    print(f"- Deduplicated data saved to: {output_file}")
    print(f"- Duplicates log saved to: {log_file}")
    if not conflicting_rows.empty:
        print(f"- Conflicting rows saved to: {warning_file}")

def main():
    parser = argparse.ArgumentParser(
        description="Deduplicate and update a CSV file based on material_id and unit prices.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input_file", help="Path to the input CSV file.")
    parser.add_argument("output_file", nargs='?', default=None, help="Path for the output CSV file (optional). Defaults to <input>_processed.csv")
    parser.add_argument("log_file", nargs='?', default=None, help="Path for the duplicates log file (optional). Defaults to <input>_duplicates.log")
    parser.add_argument("warning_file", nargs='?', default=None, help="Path for the warnings CSV file (optional). Defaults to <input>_warnings.csv")

    args = parser.parse_args()

    input_dir, input_filename = os.path.split(args.input_file)
    input_name, input_ext = os.path.splitext(input_filename)

    output_file = args.output_file or os.path.join(input_dir, f"{input_name}_processed{input_ext}")
    log_file = args.log_file or os.path.join(input_dir, f"{input_name}_duplicates.log")
    warning_file = args.warning_file or os.path.join(input_dir, f"{input_name}_warnings.csv")

    process_csv(args.input_file, output_file, log_file, warning_file)

def process_dataframe(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict], List[Dict]]:
    """处理DataFrame数据，进行去重和价格更新
    
    Args:
        df: 输入的DataFrame
        
    Returns:
        Tuple[pd.DataFrame, List[Dict], List[Dict]]: (处理后的DataFrame, 重复记录列表, 警告记录列表)
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始处理DataFrame，共 {len(df)} 行")
    
    # 检查必要字段
    required_fields = ['material_id', 'inchified_smiles', 'unit_price']
    missing_fields = [field for field in required_fields if field not in df.columns]
    if missing_fields:
        raise ValueError(f"DataFrame中缺少必要字段: {missing_fields}")
    
    duplicate_records = []
    warning_records = []
    
    # --- 去重步骤 ---
    logger.info("基于material_id进行去重...")
    
    # 找到所有基于material_id的重复项
    duplicates_mask = df.duplicated(subset=['material_id'], keep=False)
    duplicated_df = df[duplicates_mask]
    
    if not duplicated_df.empty:
        logger.info(f"发现 {len(duplicated_df)} 行重复数据")
        
        # 按material_id分组处理重复项
        for material_id, group in duplicated_df.groupby('material_id'):
            if len(group) > 1:
                # 检查是否为简单重复（所有字段都相同）
                if group.drop_duplicates().shape[0] == 1:
                    # 简单重复，记录重复信息
                    duplicate_records.extend([{
                        'material_id': material_id,
                        'duplicate_count': len(group),
                        'type': 'simple_duplicate',
                        'data': group.iloc[0].to_dict()
                    }])
                else:
                    # 冲突重复，记录警告
                    warning_records.extend([{
                        'material_id': material_id,
                        'conflict_count': len(group),
                        'type': 'conflicting_duplicate',
                        'conflicting_rows': group.to_dict('records')
                    }])
    
    # 保留每个material_id的第一次出现
    deduplicated_df = df.drop_duplicates(subset=['material_id'], keep='first')
    logger.info(f"去重后剩余 {len(deduplicated_df)} 行")
    
    # --- 价格更新步骤 ---
    logger.info("更新lowest_unit_price字段...")
    
    # 初始化lowest_unit_price字段
    deduplicated_df = deduplicated_df.copy()
    deduplicated_df['lowest_unit_price'] = False
    
    # 按inchified_smiles分组，找到每组中unit_price最低的行
    for inchi_smi, group in deduplicated_df.groupby('inchified_smiles'):
        if pd.isna(inchi_smi) or inchi_smi == '':
            continue
        
        # 找到unit_price最小的行的索引
        min_price_idx = group['unit_price'].idxmin()
        
        # 设置该行的lowest_unit_price为True
        deduplicated_df.loc[min_price_idx, 'lowest_unit_price'] = True
    
    logger.info(f"处理完成: 输出 {len(deduplicated_df)} 行, 重复记录 {len(duplicate_records)} 条, 警告 {len(warning_records)} 条")
    
    return deduplicated_df, duplicate_records, warning_records

def deduplicate_and_update_dataframe(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
    """去重和更新DataFrame
    
    Args:
        df: 输入的DataFrame
        
    Returns:
        Tuple[pd.DataFrame, List[Dict]]: (处理后的DataFrame, 所有记录列表(包括重复和警告))
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始去重和更新DataFrame，共 {len(df)} 行")
    
    processed_df, duplicate_records, warning_records = process_dataframe(df)
    
    # 合并所有记录
    all_records = []
    all_records.extend([{**record, 'record_type': 'duplicate'} for record in duplicate_records])
    all_records.extend([{**record, 'record_type': 'warning'} for record in warning_records])
    
    return processed_df, all_records

if __name__ == "__main__":
    main()