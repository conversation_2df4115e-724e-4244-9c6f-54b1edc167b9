#!/usr/bin/env python3
"""
分析两个CSV文件的差异
"""

import pandas as pd
import sys
from pathlib import Path

def analyze_csv_differences(file1_path: str, file2_path: str):
    """分析两个CSV文件的差异"""
    print(f"📊 分析文件差异...")
    print(f"文件1: {file1_path}")
    print(f"文件2: {file2_path}")
    
    # 读取文件
    df1 = pd.read_csv(file1_path)
    df2 = pd.read_csv(file2_path)
    
    print(f"\n📈 基本统计:")
    print(f"文件1行数: {len(df1)}")
    print(f"文件2行数: {len(df2)}")
    print(f"文件1列数: {len(df1.columns)}")
    print(f"文件2列数: {len(df2.columns)}")
    
    # 检查列名
    if list(df1.columns) != list(df2.columns):
        print(f"\n❌ 列名不匹配:")
        print(f"文件1列名: {list(df1.columns)}")
        print(f"文件2列名: {list(df2.columns)}")
        return
    else:
        print(f"\n✅ 列名匹配")
    
    # 检查数据类型
    print(f"\n📋 数据类型对比:")
    for col in df1.columns:
        if df1[col].dtype != df2[col].dtype:
            print(f"❌ {col}: {df1[col].dtype} vs {df2[col].dtype}")
        else:
            print(f"✅ {col}: {df1[col].dtype}")
    
    # 转换为字符串进行对比
    print(f"\n🔄 转换为字符串后对比...")
    df1_str = df1.astype(str)
    df2_str = df2.astype(str)
    
    # 逐列对比
    different_cols = []
    for col in df1.columns:
        if not df1_str[col].equals(df2_str[col]):
            different_cols.append(col)
            print(f"❌ 列 '{col}' 存在差异")
            
            # 找出不同的行
            diff_mask = df1_str[col] != df2_str[col]
            diff_count = diff_mask.sum()
            print(f"   差异行数: {diff_count}")
            
            if diff_count > 0:
                print(f"   差异示例:")
                diff_indices = df1_str[diff_mask].index[:5]
                for idx in diff_indices:
                    val1 = df1_str.loc[idx, col]
                    val2 = df2_str.loc[idx, col]
                    print(f"     行{idx}: '{val1}' vs '{val2}'")
                    # 如果是codes或pubchem_safety_link字段，显示更多信息
                    if col in ['codes', 'pubchem_safety_link']:
                        material_id = df1_str.loc[idx, 'material_id'] if 'material_id' in df1_str.columns else 'N/A'
                        print(f"       material_id: {material_id}")
        else:
            print(f"✅ 列 '{col}' 完全一致")
    
    if not different_cols:
        print(f"\n🎉 所有数据完全一致！")
    else:
        print(f"\n⚠️ 发现 {len(different_cols)} 列存在差异: {different_cols}")
    
    # 尝试排序后对比
    print(f"\n🔄 尝试排序后对比...")
    try:
        # 按所有列排序
        df1_sorted = df1_str.sort_values(by=df1_str.columns.tolist()).reset_index(drop=True)
        df2_sorted = df2_str.sort_values(by=df2_str.columns.tolist()).reset_index(drop=True)
        
        if df1_sorted.equals(df2_sorted):
            print(f"✅ 排序后数据完全一致！")
        else:
            print(f"❌ 排序后仍存在差异")
    except Exception as e:
        print(f"❌ 排序失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python analyze_differences.py <file1> <file2>")
        sys.exit(1)
    
    file1 = sys.argv[1]
    file2 = sys.argv[2]
    
    if not Path(file1).exists():
        print(f"错误: 文件不存在 {file1}")
        sys.exit(1)
    
    if not Path(file2).exists():
        print(f"错误: 文件不存在 {file2}")
        sys.exit(1)
    
    analyze_csv_differences(file1, file2)