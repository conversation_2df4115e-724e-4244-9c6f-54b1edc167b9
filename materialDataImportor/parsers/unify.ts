import { MaterialItem } from "../..";
import { transformCsv } from "../transformCsv";
import { getNumber, parseSpecification } from "../utils";
import { HeaderArray } from "@fast-csv/parse";

export const headers = [
  "material_lib",
  "canonical_smiles",
  "inchified_smiles",
  "material_id",
  "cas_no",
  "name_en",
  "name_zh",
  "quantity",
  "unit",
  "price",
  "in_stock",
  "max_delivery_days",
  "min_delivery_days",
  "purity",
  "source",
  "source_link",
  "extension",
  "a",
] as const;

export type MculeItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = (row: MculeItem): MaterialItem => {
  let quantity = getNumber(row.quantity);
  let unit = row.unit;
  if (!quantity) {
    const a = parseSpecification(unit);
    quantity = a.quantity;
    unit = a.unit;
  }
  const source_link = row.source_link.startsWith(
    "https://www.labgogo.com/product"
  )
    ? row.source_link.replace(
        "https://www.labgogo.com/product",
        "https://www.leyan.com"
      )
    : row.source_link;
  return {
    material_lib: getNumber(row.material_lib) || 0,

    canonical_smiles: row.canonical_smiles,
    inchified_smiles: row.inchified_smiles,
    material_id: `${row.material_id}`,
    cas_no: row.cas_no,
    name_en: row.name_en,
    name_zh: row.name_zh,

    quantity,
    unit,
    price: getNumber(row.price) || 0,
    in_stock: !!row.in_stock,
    max_delivery_days: getNumber(row.max_delivery_days),
    min_delivery_days: getNumber(row.min_delivery_days),
    purity: row.purity === row.name_en ? "" : row.purity,
    unified_unit: "",
    unit_price: 0,
    unit_quantity: 0,

    source: row.source,
    source_link,
    extension: undefined,
  };
};

export const mculeMain = async () => {
  transformCsv<MculeItem, MaterialItem>(
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule.csv",
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule_out.csv",
    "/Users/<USER>/Work/brain-server/scripts/data/materials/mcule_err.csv",
    {
      headers: headers as unknown as string[],
      transform: transformWithoutSmiles,
    },
    false
  );
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
