import { MaterialItem } from "../../..";
import { dollarToRmbRate, getNumber } from "../../utils";
import { HeaderArray } from "@fast-csv/parse";
import { addUnitPrice } from "../fillInfo/unitPrice";
import { providerIdMap } from "./idMap";

export const headers = [
  "version_id",
  "smiles",
  "highest_tier",
  "average_price",
] as const;

export type EmoleculeItem = {
  [key in (typeof headers)[number]]: string;
};

export const transformWithoutSmiles = async (
  row: EmoleculeItem
): Promise<MaterialItem | false> => {
  const price = (getNumber(row.average_price) || 0) * dollarToRmbRate;
  const mid = {
    material_lib: providerIdMap.emolecule,

    canonical_smiles: row.smiles,
    inchified_smiles: "",
    material_id: `emolecule_${row.version_id}`,
    cas_no: "",
    name_en: "",
    name_zh: "",

    quantity: 1,
    unit: "g",
    price,
    in_stock: undefined,
    max_delivery_days: undefined,
    min_delivery_days: undefined,
    purity: undefined,

    source: "emolecule",
    source_link: `https://orderbb.emolecules.com/search/#?smiles=${encodeURIComponent(
      row.smiles
    )}&searchtype=ex&simlimit=1&system-type=BB&p=1`,
    extension: undefined,

    codes: [],
    pubchem_safety_link: "",
    unified_smiles: "",
  };

  const result = addUnitPrice(mid);
  if (result.unit_price > 250 * dollarToRmbRate) return false;
  return result;
};

export const config = {
  headers: headers as unknown as HeaderArray,
  transform: transformWithoutSmiles,
};
