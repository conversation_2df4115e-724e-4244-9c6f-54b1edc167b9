import { promises } from "fs";
import { getPubchemInfoFromApi } from "./api";
import { parseFile } from "./html";

export interface ParsedInfo {
  entryTerm: string[];
  synonyms: string[];
  casLikes: string[];
  computedProperties: Record<string, string>;
  experimentalProperties: Record<string, { origin: string; parsed: string }[]>;
}

const loadJsonData = async (path: string): Promise<any> => {
  try {
    const data = await promises.readFile(path, "utf-8");
    const jsonData = JSON.parse(data);
    return jsonData;
  } catch (err) {
    console.error("Error reading or parsing JSON file:", err);
  }
};

const writeToJson = async (data: string, path: string, id: string | number) => {
  await promises.writeFile(`${path}/${id}.json`, data);
};

const handleHtml = async (id: number | string) => {
  const parsed = await parseFile(`data/pubchem/htmls/${id}.html`);
  await writeToJson(JSON.stringify(parsed), "data/pubchem/json", id);
};

export const handleApi = async (
  id: number,
  outPath: string = "data/pubchem/json/api"
) => {
  const parsed = await getPubchemInfoFromApi(id);
  await writeToJson(JSON.stringify(parsed), outPath, id);
};

export const handleIdsWithSynonyms = async (
  ids: number[]
): Promise<Record<number, string[]>> => {
  const parsed = await Promise.all(ids.map(getPubchemInfoFromApi));
  const save = ids.reduce<Record<number, string[]>>((acc, cur, index) => {
    const data = parsed[index];
    acc[cur] = [...data.entryTerm, ...data.synonyms];
    return acc;
  }, {});
  return save;
};

const main = async () => {
  // const json = await handleIdsWithSynonyms([
  //   11, 174, 176, 180, 222, 243, 260, 284, 311, 313, 612, 679, 700, 702, 767,
  //   781, 875, 887, 944, 962, 971, 1004, 1049, 1068, 1110, 1118, 1140, 3776,
  //   5943, 6101, 6212, 6228, 6328, 6340, 6342, 6344, 6356, 6395, 6412, 6422,
  //   6954, 7237, 7301, 7371, 7582, 7809, 7813, 7914, 7915, 7918, 7929, 7965,
  //   7972, 8021, 8028, 8058, 8078, 8082, 8113, 8471, 8857, 8900, 15413, 24247,
  //   24386, 24841, 31275, 31374, 62406, 439655, 444266, 444305, 444972,
  // ]);
  // await writeToJson(JSON.stringify(json), "data/pubchem/json", 0);

  const names = (await loadJsonData("data/pubchem/json/0.json")) as Record<
    string,
    string[]
  >;
  const smiles = (await loadJsonData("data/pubchem/json/1.json")) as {
    cid: string;
    smi: string;
  }[];
  const smilesToNames = smiles.reduce<Record<string, string[]>>((acc, cur) => {
    const smiNames = names[`${cur.cid}`];
    acc[cur.smi] = smiNames;
    return acc;
  }, {});
  const arrs = Object.keys(smilesToNames).map((s, i) => ({
    id: i + 1,
    canonical_smiles: s,
    type: "common_solvent",
    synonyms: smilesToNames[s],
  }));
  await writeToJson(JSON.stringify(arrs), "data/pubchem/json", 2);
};

main();
